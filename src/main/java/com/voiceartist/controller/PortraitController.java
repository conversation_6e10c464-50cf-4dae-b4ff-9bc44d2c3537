package com.voiceartist.controller;

import com.voiceartist.dto.request.PortraitGenerateRequest;
import com.voiceartist.dto.response.ApiResponse;
import com.voiceartist.dto.response.PortraitTaskResponse;
import com.voiceartist.service.PortraitService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 肖像生成控制器
 */
@Slf4j
@RestController
@RequestMapping("/portrait")
@RequiredArgsConstructor
@Tag(name = "肖像生成", description = "AI肖像生成相关接口")
public class PortraitController {

    private final PortraitService portraitService;

    /**
     * 创建肖像生成任务
     */
    @PostMapping("/generate")
    @Operation(summary = "创建肖像生成任务", description = "根据语音特征和用户设置创建肖像生成任务")
    public ApiResponse<PortraitTaskResponse> generatePortrait(
            @RequestAttribute("userId") Long userId,
            @Valid @RequestBody PortraitGenerateRequest request) {
        
        log.info("创建肖像生成任务: userId={}, voiceId={}, style={}", 
                userId, request.getVoiceId(), request.getArtStyle());
        
        PortraitTaskResponse response = portraitService.generatePortrait(userId, request);
        
        log.info("肖像生成任务创建成功: userId={}, taskId={}", userId, response.getTaskId());
        return ApiResponse.success("任务创建成功", response);
    }

    /**
     * 查询生成任务状态
     */
    @GetMapping("/task/{taskId}")
    @Operation(summary = "查询生成任务状态", description = "查询肖像生成任务的当前状态和进度")
    public ApiResponse<PortraitTaskResponse> getTaskStatus(
            @RequestAttribute("userId") Long userId,
            @PathVariable String taskId) {
        
        log.info("查询任务状态: userId={}, taskId={}", userId, taskId);
        
        PortraitTaskResponse response = portraitService.getTaskStatus(taskId, userId);
        
        return ApiResponse.success(response);
    }

    /**
     * 重新生成肖像
     */
    @PostMapping("/regenerate")
    @Operation(summary = "重新生成肖像", description = "基于原任务重新生成肖像，可调整生成参数")
    public ApiResponse<PortraitTaskResponse> regeneratePortrait(
            @RequestAttribute("userId") Long userId,
            @RequestBody RegenerateRequest request) {
        
        log.info("重新生成肖像: userId={}, originalTaskId={}", userId, request.getOriginalTaskId());
        
        // 构建新的生成请求
        PortraitGenerateRequest generateRequest = new PortraitGenerateRequest();
        generateRequest.setVoiceId(request.getVoiceId());
        generateRequest.setArtStyle(request.getArtStyle());
        generateRequest.setGenderPreference(request.getGenderPreference());
        generateRequest.setColorTheme(request.getColorTheme());
        generateRequest.setBackgroundSetting(request.getBackgroundSetting());
        generateRequest.setTitle(request.getTitle());
        generateRequest.setDescription(request.getDescription());
        
        PortraitTaskResponse response = portraitService.regeneratePortrait(
                userId, request.getOriginalTaskId(), generateRequest);
        
        log.info("重新生成任务创建成功: userId={}, newTaskId={}", userId, response.getTaskId());
        return ApiResponse.success("重新生成任务创建成功", response);
    }

    /**
     * 重新生成请求DTO
     */
    public static class RegenerateRequest {
        private String originalTaskId;
        private String voiceId;
        private com.voiceartist.entity.PortraitTask.ArtStyle artStyle;
        private com.voiceartist.entity.PortraitTask.GenderPreference genderPreference;
        private com.voiceartist.entity.PortraitTask.ColorTheme colorTheme;
        private String backgroundSetting;
        private String title;
        private String description;

        // Getters and Setters
        public String getOriginalTaskId() { return originalTaskId; }
        public void setOriginalTaskId(String originalTaskId) { this.originalTaskId = originalTaskId; }
        public String getVoiceId() { return voiceId; }
        public void setVoiceId(String voiceId) { this.voiceId = voiceId; }
        public com.voiceartist.entity.PortraitTask.ArtStyle getArtStyle() { return artStyle; }
        public void setArtStyle(com.voiceartist.entity.PortraitTask.ArtStyle artStyle) { this.artStyle = artStyle; }
        public com.voiceartist.entity.PortraitTask.GenderPreference getGenderPreference() { return genderPreference; }
        public void setGenderPreference(com.voiceartist.entity.PortraitTask.GenderPreference genderPreference) { this.genderPreference = genderPreference; }
        public com.voiceartist.entity.PortraitTask.ColorTheme getColorTheme() { return colorTheme; }
        public void setColorTheme(com.voiceartist.entity.PortraitTask.ColorTheme colorTheme) { this.colorTheme = colorTheme; }
        public String getBackgroundSetting() { return backgroundSetting; }
        public void setBackgroundSetting(String backgroundSetting) { this.backgroundSetting = backgroundSetting; }
        public String getTitle() { return title; }
        public void setTitle(String title) { this.title = title; }
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
    }
}
