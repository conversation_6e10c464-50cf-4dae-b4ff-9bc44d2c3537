package com.voiceartist.repository;

import com.voiceartist.entity.Artwork;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * 作品数据访问接口
 */
@Repository
public interface ArtworkRepository extends JpaRepository<Artwork, Long> {

    /**
     * 根据作品ID查找作品
     */
    Optional<Artwork> findByArtworkId(String artworkId);

    /**
     * 根据用户ID查找作品列表
     */
    Page<Artwork> findByUserIdAndStatusOrderByCreateTimeDesc(Long userId, 
                                                            Artwork.ArtworkStatus status, 
                                                            Pageable pageable);

    /**
     * 查找公开展示的作品
     */
    Page<Artwork> findByIsPublicTrueAndStatusOrderByCreateTimeDesc(Artwork.ArtworkStatus status, 
                                                                  Pageable pageable);

    /**
     * 查找热门作品（按点赞数排序）
     */
    Page<Artwork> findByIsPublicTrueAndStatusOrderByLikeCountDescCreateTimeDesc(Artwork.ArtworkStatus status, 
                                                                               Pageable pageable);

    /**
     * 根据肖像任务ID查找作品
     */
    Optional<Artwork> findByPortraitTaskId(Long portraitTaskId);

    /**
     * 增加查看次数
     */
    @Modifying
    @Query("UPDATE Artwork a SET a.viewCount = a.viewCount + 1 WHERE a.artworkId = :artworkId")
    int incrementViewCount(@Param("artworkId") String artworkId);

    /**
     * 增加点赞数
     */
    @Modifying
    @Query("UPDATE Artwork a SET a.likeCount = a.likeCount + 1 WHERE a.artworkId = :artworkId")
    int incrementLikeCount(@Param("artworkId") String artworkId);

    /**
     * 减少点赞数
     */
    @Modifying
    @Query("UPDATE Artwork a SET a.likeCount = a.likeCount - 1 WHERE a.artworkId = :artworkId AND a.likeCount > 0")
    int decrementLikeCount(@Param("artworkId") String artworkId);

    /**
     * 增加分享次数
     */
    @Modifying
    @Query("UPDATE Artwork a SET a.shareCount = a.shareCount + 1 WHERE a.artworkId = :artworkId")
    int incrementShareCount(@Param("artworkId") String artworkId);

    /**
     * 查询用户作品总数
     */
    long countByUserIdAndStatus(Long userId, Artwork.ArtworkStatus status);

    /**
     * 查询公开作品总数
     */
    long countByIsPublicTrueAndStatus(Artwork.ArtworkStatus status);
}
