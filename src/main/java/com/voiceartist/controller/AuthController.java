package com.voiceartist.controller;

import com.voiceartist.dto.request.LoginRequest;
import com.voiceartist.dto.response.ApiResponse;
import com.voiceartist.dto.response.LoginResponse;
import com.voiceartist.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 认证控制器
 */
@Slf4j
@RestController
@RequestMapping("/auth")
@RequiredArgsConstructor
@Tag(name = "认证管理", description = "用户登录认证相关接口")
public class AuthController {

    private final UserService userService;

    /**
     * 微信小程序登录
     */
    @PostMapping("/login")
    @Operation(summary = "微信小程序登录", description = "通过微信小程序code进行用户登录")
    public ApiResponse<LoginResponse> login(@Valid @RequestBody LoginRequest request) {
        log.info("用户登录请求: code={}", request.getCode());
        
        LoginResponse response = userService.login(request);
        
        log.info("用户登录成功: userId={}", response.getUserId());
        return ApiResponse.success("登录成功", response);
    }
}
