package com.voiceartist.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;

/**
 * 用户实体类
 */
@Data
@Entity
@Table(name = "users")
@EqualsAndHashCode(callSuper = false)
public class User {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 微信小程序用户唯一标识
     */
    @Column(name = "openid", unique = true, nullable = false)
    private String openid;

    /**
     * 微信用户唯一标识（跨应用）
     */
    @Column(name = "unionid")
    private String unionid;

    /**
     * 用户昵称
     */
    @Column(name = "nickname")
    private String nickname;

    /**
     * 用户头像URL
     */
    @Column(name = "avatar_url")
    private String avatarUrl;

    /**
     * 性别：0-未知，1-男，2-女
     */
    @Column(name = "gender")
    private Integer gender;

    /**
     * 城市
     */
    @Column(name = "city")
    private String city;

    /**
     * 省份
     */
    @Column(name = "province")
    private String province;

    /**
     * 国家
     */
    @Column(name = "country")
    private String country;

    /**
     * 是否为会员用户
     */
    @Column(name = "is_premium", nullable = false, columnDefinition = "BOOLEAN DEFAULT FALSE")
    private Boolean isPremium = false;

    /**
     * 会员到期时间
     */
    @Column(name = "premium_expire_time")
    private LocalDateTime premiumExpireTime;

    /**
     * 今日使用次数
     */
    @Column(name = "daily_usage_count", nullable = false, columnDefinition = "INT DEFAULT 0")
    private Integer dailyUsageCount = 0;

    /**
     * 总使用次数
     */
    @Column(name = "total_usage_count", nullable = false, columnDefinition = "INT DEFAULT 0")
    private Integer totalUsageCount = 0;

    /**
     * 最后使用时间
     */
    @Column(name = "last_usage_time")
    private LocalDateTime lastUsageTime;

    /**
     * 用户状态：0-正常，1-禁用
     */
    @Column(name = "status", nullable = false, columnDefinition = "INT DEFAULT 0")
    private Integer status = 0;

    /**
     * 创建时间
     */
    @CreationTimestamp
    @Column(name = "create_time", nullable = false, updatable = false)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @UpdateTimestamp
    @Column(name = "update_time", nullable = false)
    private LocalDateTime updateTime;

    /**
     * 检查是否为会员用户
     */
    public boolean isValidPremium() {
        return isPremium != null && isPremium && 
               premiumExpireTime != null && 
               premiumExpireTime.isAfter(LocalDateTime.now());
    }

    /**
     * 检查今日是否还有使用次数
     */
    public boolean hasUsageQuota() {
        int dailyLimit = isValidPremium() ? 100 : 3; // 会员100次，免费用户3次
        return dailyUsageCount < dailyLimit;
    }
}
