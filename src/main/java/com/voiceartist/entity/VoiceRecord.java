package com.voiceartist.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;

/**
 * 语音记录实体类
 */
@Data
@Entity
@Table(name = "voice_records")
@EqualsAndHashCode(callSuper = false)
public class VoiceRecord {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 语音记录唯一标识
     */
    @Column(name = "voice_id", unique = true, nullable = false)
    private String voiceId;

    /**
     * 用户ID
     */
    @Column(name = "user_id", nullable = false)
    private Long userId;

    /**
     * 原始文件名
     */
    @Column(name = "original_filename")
    private String originalFilename;

    /**
     * 存储文件名
     */
    @Column(name = "stored_filename", nullable = false)
    private String storedFilename;

    /**
     * 文件路径
     */
    @Column(name = "file_path", nullable = false)
    private String filePath;

    /**
     * 文件URL
     */
    @Column(name = "file_url")
    private String fileUrl;

    /**
     * 文件大小（字节）
     */
    @Column(name = "file_size")
    private Long fileSize;

    /**
     * 音频时长（秒）
     */
    @Column(name = "duration")
    private Integer duration;

    /**
     * 音频格式
     */
    @Column(name = "format")
    private String format;

    /**
     * 采样率
     */
    @Column(name = "sample_rate")
    private Integer sampleRate;

    /**
     * 处理状态：UPLOADED-已上传，PROCESSING-处理中，COMPLETED-完成，FAILED-失败
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private ProcessStatus status = ProcessStatus.UPLOADED;

    /**
     * 语音特征JSON数据
     */
    @Column(name = "voice_features", columnDefinition = "TEXT")
    private String voiceFeatures;

    /**
     * 错误信息
     */
    @Column(name = "error_message")
    private String errorMessage;

    /**
     * 创建时间
     */
    @CreationTimestamp
    @Column(name = "create_time", nullable = false, updatable = false)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @UpdateTimestamp
    @Column(name = "update_time", nullable = false)
    private LocalDateTime updateTime;

    /**
     * 处理状态枚举
     */
    public enum ProcessStatus {
        UPLOADED("已上传"),
        PROCESSING("处理中"),
        COMPLETED("完成"),
        FAILED("失败");

        private final String description;

        ProcessStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
