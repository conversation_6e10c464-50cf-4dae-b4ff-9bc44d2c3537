package com.voiceartist.dto.request;

import com.voiceartist.entity.PortraitTask;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 肖像生成请求DTO
 */
@Data
public class PortraitGenerateRequest {

    /**
     * 语音记录ID
     */
    @NotBlank(message = "语音记录ID不能为空")
    private String voiceId;

    /**
     * 艺术风格
     */
    @NotNull(message = "艺术风格不能为空")
    private PortraitTask.ArtStyle artStyle;

    /**
     * 性别偏好
     */
    @NotNull(message = "性别偏好不能为空")
    private PortraitTask.GenderPreference genderPreference;

    /**
     * 色彩主题
     */
    @NotNull(message = "色彩主题不能为空")
    private PortraitTask.ColorTheme colorTheme;

    /**
     * 背景设置（可选）
     */
    private String backgroundSetting;

    /**
     * 作品标题（可选）
     */
    private String title;

    /**
     * 作品描述（可选）
     */
    private String description;
}
