server:
  port: 8080
  servlet:
    context-path: /api/v1

spring:
  application:
    name: voice-portrait-backend
  
  datasource:
    url: ***************************************************************************************************************************
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:password}
    driver-class-name: com.mysql.cj.jdbc.Driver
    
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true
        
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}
    database: 0
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
        
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB
      
  jackson:
    time-zone: Asia/Shanghai
    date-format: yyyy-MM-dd HH:mm:ss

# 微信小程序配置
wechat:
  miniapp:
    app-id: ${WECHAT_APP_ID:your_app_id}
    app-secret: ${WECHAT_APP_SECRET:your_app_secret}
    
# JWT配置
jwt:
  secret: ${JWT_SECRET:voice_portrait_secret_key_2024}
  expiration: 604800 # 7天，单位秒
  
# 文件存储配置
file:
  upload:
    path: ${FILE_UPLOAD_PATH:/tmp/voice-portrait/uploads}
    voice-path: ${FILE_UPLOAD_PATH:/tmp/voice-portrait/uploads}/voices
    image-path: ${FILE_UPLOAD_PATH:/tmp/voice-portrait/uploads}/images
    max-size: 52428800 # 50MB
    
# AI服务配置
ai:
  service:
    base-url: ${AI_SERVICE_URL:http://localhost:8081}
    timeout: 300000 # 5分钟
    
# 业务配置
business:
  daily-limit:
    free-user: 3
    premium-user: 100
  voice:
    min-duration: 10 # 最小录音时长（秒）
    max-duration: 30 # 最大录音时长（秒）
    
# 日志配置
logging:
  level:
    com.voiceartist: DEBUG
    org.springframework.security: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/voice-portrait.log
    
# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized
