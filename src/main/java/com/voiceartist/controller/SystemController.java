package com.voiceartist.controller;

import com.voiceartist.dto.response.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 系统管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/system")
@RequiredArgsConstructor
@Tag(name = "系统管理", description = "系统状态、配置等管理接口")
public class SystemController {

    @Value("${spring.application.name}")
    private String applicationName;

    @Value("${spring.profiles.active:default}")
    private String activeProfile;

    /**
     * 系统健康检查
     */
    @GetMapping("/health")
    @Operation(summary = "健康检查", description = "检查系统运行状态")
    public ApiResponse<Map<String, Object>> health() {
        Map<String, Object> healthInfo = new HashMap<>();
        healthInfo.put("status", "UP");
        healthInfo.put("application", applicationName);
        healthInfo.put("profile", activeProfile);
        healthInfo.put("timestamp", LocalDateTime.now());
        healthInfo.put("version", "1.0.0");
        
        return ApiResponse.success(healthInfo);
    }

    /**
     * 系统信息
     */
    @GetMapping("/info")
    @Operation(summary = "系统信息", description = "获取系统基本信息")
    public ApiResponse<Map<String, Object>> info() {
        Map<String, Object> systemInfo = new HashMap<>();
        
        // 应用信息
        Map<String, Object> appInfo = new HashMap<>();
        appInfo.put("name", applicationName);
        appInfo.put("version", "1.0.0");
        appInfo.put("profile", activeProfile);
        appInfo.put("startTime", LocalDateTime.now().minusHours(1)); // 模拟启动时间
        systemInfo.put("application", appInfo);
        
        // JVM信息
        Runtime runtime = Runtime.getRuntime();
        Map<String, Object> jvmInfo = new HashMap<>();
        jvmInfo.put("javaVersion", System.getProperty("java.version"));
        jvmInfo.put("jvmName", System.getProperty("java.vm.name"));
        jvmInfo.put("totalMemory", formatBytes(runtime.totalMemory()));
        jvmInfo.put("freeMemory", formatBytes(runtime.freeMemory()));
        jvmInfo.put("maxMemory", formatBytes(runtime.maxMemory()));
        jvmInfo.put("usedMemory", formatBytes(runtime.totalMemory() - runtime.freeMemory()));
        systemInfo.put("jvm", jvmInfo);
        
        // 系统信息
        Map<String, Object> osInfo = new HashMap<>();
        osInfo.put("osName", System.getProperty("os.name"));
        osInfo.put("osVersion", System.getProperty("os.version"));
        osInfo.put("osArch", System.getProperty("os.arch"));
        osInfo.put("availableProcessors", runtime.availableProcessors());
        systemInfo.put("system", osInfo);
        
        return ApiResponse.success(systemInfo);
    }

    /**
     * 业务配置信息
     */
    @GetMapping("/config")
    @Operation(summary = "业务配置", description = "获取业务相关配置信息")
    public ApiResponse<Map<String, Object>> config() {
        Map<String, Object> configInfo = new HashMap<>();
        
        // 业务规则配置
        Map<String, Object> businessRules = new HashMap<>();
        businessRules.put("freeUserDailyLimit", 3);
        businessRules.put("premiumUserDailyLimit", 100);
        businessRules.put("voiceMinDuration", 10);
        businessRules.put("voiceMaxDuration", 30);
        businessRules.put("maxFileSize", "50MB");
        businessRules.put("supportedFormats", new String[]{"MP3", "WAV", "M4A", "AAC", "OGG"});
        configInfo.put("businessRules", businessRules);
        
        // 艺术风格配置
        Map<String, Object> artStyles = new HashMap<>();
        artStyles.put("available", new String[]{"REALISTIC", "CARTOON", "WATERCOLOR", "SKETCH"});
        artStyles.put("default", "REALISTIC");
        configInfo.put("artStyles", artStyles);
        
        // 色彩主题配置
        Map<String, Object> colorThemes = new HashMap<>();
        colorThemes.put("available", new String[]{"WARM", "COOL", "MONOCHROME", "RAINBOW"});
        colorThemes.put("default", "WARM");
        configInfo.put("colorThemes", colorThemes);
        
        return ApiResponse.success(configInfo);
    }

    /**
     * 格式化字节数
     */
    private String formatBytes(long bytes) {
        if (bytes < 1024) {
            return bytes + " B";
        } else if (bytes < 1024 * 1024) {
            return String.format("%.2f KB", bytes / 1024.0);
        } else if (bytes < 1024 * 1024 * 1024) {
            return String.format("%.2f MB", bytes / (1024.0 * 1024.0));
        } else {
            return String.format("%.2f GB", bytes / (1024.0 * 1024.0 * 1024.0));
        }
    }
}
