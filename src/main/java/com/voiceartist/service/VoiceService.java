package com.voiceartist.service;

import com.voiceartist.dto.response.VoiceUploadResponse;
import com.voiceartist.entity.VoiceRecord;
import com.voiceartist.exception.BusinessException;
import com.voiceartist.repository.VoiceRecordRepository;
import com.voiceartist.util.FileUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * 语音处理服务类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class VoiceService {

    private final VoiceRecordRepository voiceRecordRepository;
    private final UserService userService;

    @Value("${file.upload.voice-path}")
    private String voiceUploadPath;

    @Value("${file.upload.max-size}")
    private long maxFileSize;

    @Value("${business.voice.min-duration}")
    private int minDuration;

    @Value("${business.voice.max-duration}")
    private int maxDuration;

    // 支持的音频格式
    private static final List<String> SUPPORTED_FORMATS = Arrays.asList(
            "audio/mpeg", "audio/mp3", "audio/wav", "audio/m4a", "audio/aac", "audio/ogg"
    );

    /**
     * 上传语音文件
     */
    @Transactional
    public VoiceUploadResponse uploadVoice(Long userId, MultipartFile file, Integer duration) {
        try {
            // 1. 验证用户配额
            if (!userService.checkUsageQuota(userId)) {
                throw new BusinessException("今日使用次数已达上限");
            }

            // 2. 验证文件
            validateVoiceFile(file, duration);

            // 3. 保存文件
            String voiceId = UUID.randomUUID().toString();
            String storedFilename = saveVoiceFile(file, voiceId);

            // 4. 创建语音记录
            VoiceRecord voiceRecord = createVoiceRecord(userId, voiceId, file, storedFilename, duration);
            voiceRecord = voiceRecordRepository.save(voiceRecord);

            // 5. 异步处理语音特征提取
            processVoiceAsync(voiceRecord.getId());

            // 6. 构建响应
            return buildVoiceUploadResponse(voiceRecord);

        } catch (Exception e) {
            log.error("语音上传失败: userId={}, error={}", userId, e.getMessage(), e);
            if (e instanceof BusinessException) {
                throw e;
            }
            throw new BusinessException("语音上传失败，请重试");
        }
    }

    /**
     * 根据语音ID获取语音记录
     */
    public VoiceRecord getVoiceRecord(String voiceId) {
        return voiceRecordRepository.findByVoiceId(voiceId)
                .orElseThrow(() -> new BusinessException("语音记录不存在"));
    }

    /**
     * 获取语音处理状态
     */
    public VoiceRecord getVoiceStatus(String voiceId, Long userId) {
        VoiceRecord voiceRecord = getVoiceRecord(voiceId);
        
        // 验证用户权限
        if (!voiceRecord.getUserId().equals(userId)) {
            throw new BusinessException("无权访问该语音记录");
        }
        
        return voiceRecord;
    }

    /**
     * 验证语音文件
     */
    private void validateVoiceFile(MultipartFile file, Integer duration) {
        // 检查文件是否为空
        if (file == null || file.isEmpty()) {
            throw new BusinessException("请选择要上传的语音文件");
        }

        // 检查文件大小
        if (file.getSize() > maxFileSize) {
            throw new BusinessException("文件大小不能超过50MB");
        }

        // 检查文件格式
        String contentType = file.getContentType();
        if (contentType == null || !SUPPORTED_FORMATS.contains(contentType.toLowerCase())) {
            throw new BusinessException("不支持的音频格式，请上传MP3、WAV、M4A、AAC或OGG格式的文件");
        }

        // 检查时长
        if (duration == null || duration < minDuration || duration > maxDuration) {
            throw new BusinessException(String.format("录音时长必须在%d-%d秒之间", minDuration, maxDuration));
        }
    }

    /**
     * 保存语音文件
     */
    private String saveVoiceFile(MultipartFile file, String voiceId) throws IOException {
        // 确保上传目录存在
        Path uploadDir = Paths.get(voiceUploadPath);
        if (!Files.exists(uploadDir)) {
            Files.createDirectories(uploadDir);
        }

        // 生成文件名
        String originalFilename = file.getOriginalFilename();
        String extension = FileUtil.getFileExtension(originalFilename);
        String storedFilename = voiceId + "." + extension;

        // 保存文件
        Path filePath = uploadDir.resolve(storedFilename);
        Files.copy(file.getInputStream(), filePath);

        log.info("语音文件保存成功: voiceId={}, filename={}", voiceId, storedFilename);
        return storedFilename;
    }

    /**
     * 创建语音记录
     */
    private VoiceRecord createVoiceRecord(Long userId, String voiceId, MultipartFile file, 
                                        String storedFilename, Integer duration) {
        VoiceRecord voiceRecord = new VoiceRecord();
        voiceRecord.setVoiceId(voiceId);
        voiceRecord.setUserId(userId);
        voiceRecord.setOriginalFilename(file.getOriginalFilename());
        voiceRecord.setStoredFilename(storedFilename);
        voiceRecord.setFilePath(Paths.get(voiceUploadPath, storedFilename).toString());
        voiceRecord.setFileUrl("/api/v1/voice/" + voiceId + "/download");
        voiceRecord.setFileSize(file.getSize());
        voiceRecord.setDuration(duration);
        voiceRecord.setFormat(FileUtil.getFileExtension(file.getOriginalFilename()));
        voiceRecord.setStatus(VoiceRecord.ProcessStatus.UPLOADED);
        
        return voiceRecord;
    }

    /**
     * 异步处理语音特征提取
     */
    @Async
    public void processVoiceAsync(Long voiceRecordId) {
        try {
            log.info("开始处理语音特征提取: voiceRecordId={}", voiceRecordId);
            
            Optional<VoiceRecord> optionalRecord = voiceRecordRepository.findById(voiceRecordId);
            if (optionalRecord.isEmpty()) {
                log.error("语音记录不存在: voiceRecordId={}", voiceRecordId);
                return;
            }

            VoiceRecord voiceRecord = optionalRecord.get();
            voiceRecord.setStatus(VoiceRecord.ProcessStatus.PROCESSING);
            voiceRecordRepository.save(voiceRecord);

            // TODO: 调用AI服务进行语音特征提取
            // 这里暂时模拟处理过程
            Thread.sleep(5000); // 模拟处理时间

            // 模拟提取的语音特征
            String mockFeatures = """
                {
                    "pitch": 220.5,
                    "tempo": 120,
                    "emotion": "neutral",
                    "energy": 0.7,
                    "spectral_centroid": 1500.2,
                    "mfcc": [1.2, -0.5, 0.8, 1.1, -0.3]
                }
                """;

            voiceRecord.setVoiceFeatures(mockFeatures);
            voiceRecord.setStatus(VoiceRecord.ProcessStatus.COMPLETED);
            voiceRecordRepository.save(voiceRecord);

            log.info("语音特征提取完成: voiceRecordId={}", voiceRecordId);

        } catch (Exception e) {
            log.error("语音特征提取失败: voiceRecordId={}, error={}", voiceRecordId, e.getMessage(), e);
            
            // 更新失败状态
            voiceRecordRepository.findById(voiceRecordId).ifPresent(record -> {
                record.setStatus(VoiceRecord.ProcessStatus.FAILED);
                record.setErrorMessage(e.getMessage());
                voiceRecordRepository.save(record);
            });
        }
    }

    /**
     * 构建语音上传响应
     */
    private VoiceUploadResponse buildVoiceUploadResponse(VoiceRecord voiceRecord) {
        VoiceUploadResponse response = new VoiceUploadResponse();
        response.setVoiceId(voiceRecord.getVoiceId());
        response.setFileUrl(voiceRecord.getFileUrl());
        response.setDuration(voiceRecord.getDuration());
        response.setFileSize(voiceRecord.getFileSize());
        response.setFormat(voiceRecord.getFormat());
        response.setStatus(voiceRecord.getStatus());
        response.setCreateTime(voiceRecord.getCreateTime());
        return response;
    }
}
