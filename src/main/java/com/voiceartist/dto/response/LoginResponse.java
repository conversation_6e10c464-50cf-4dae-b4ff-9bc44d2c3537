package com.voiceartist.dto.response;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 登录响应DTO
 */
@Data
public class LoginResponse {

    /**
     * JWT访问令牌
     */
    private String token;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 用户信息
     */
    private UserInfo userInfo;

    @Data
    public static class UserInfo {
        /**
         * 用户昵称
         */
        private String nickname;

        /**
         * 用户头像URL
         */
        private String avatarUrl;

        /**
         * 是否为会员用户
         */
        private Boolean isPremium;

        /**
         * 会员到期时间
         */
        private LocalDateTime premiumExpireTime;

        /**
         * 今日使用次数
         */
        private Integer dailyUsageCount;

        /**
         * 总使用次数
         */
        private Integer totalUsageCount;

        /**
         * 创建时间
         */
        private LocalDateTime createTime;
    }
}
