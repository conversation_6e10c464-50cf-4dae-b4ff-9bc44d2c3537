package com.voiceartist.repository;

import com.voiceartist.entity.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.Optional;

/**
 * 用户数据访问接口
 */
@Repository
public interface UserRepository extends JpaRepository<User, Long> {

    /**
     * 根据openid查找用户
     */
    Optional<User> findByOpenid(String openid);

    /**
     * 根据openid和状态查找用户
     */
    Optional<User> findByOpenidAndStatus(String openid, Integer status);

    /**
     * 检查openid是否存在
     */
    boolean existsByOpenid(String openid);

    /**
     * 更新用户使用次数
     */
    @Modifying
    @Query("UPDATE User u SET u.dailyUsageCount = u.dailyUsageCount + 1, " +
           "u.totalUsageCount = u.totalUsageCount + 1, " +
           "u.lastUsageTime = :lastUsageTime " +
           "WHERE u.id = :userId")
    int incrementUsageCount(@Param("userId") Long userId, 
                           @Param("lastUsageTime") LocalDateTime lastUsageTime);

    /**
     * 重置每日使用次数（定时任务使用）
     */
    @Modifying
    @Query("UPDATE User u SET u.dailyUsageCount = 0")
    int resetDailyUsageCount();

    /**
     * 查询今日新增用户数
     */
    @Query("SELECT COUNT(u) FROM User u WHERE DATE(u.createTime) = CURRENT_DATE")
    long countTodayNewUsers();

    /**
     * 查询活跃用户数（最近7天有使用记录）
     */
    @Query("SELECT COUNT(u) FROM User u WHERE u.lastUsageTime >= :sevenDaysAgo")
    long countActiveUsers(@Param("sevenDaysAgo") LocalDateTime sevenDaysAgo);

    /**
     * 查询会员用户数
     */
    @Query("SELECT COUNT(u) FROM User u WHERE u.isPremium = true AND u.premiumExpireTime > CURRENT_TIMESTAMP")
    long countPremiumUsers();
}
