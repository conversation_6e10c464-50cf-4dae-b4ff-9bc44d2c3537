-- 语音肖像生成系统数据库初始化脚本

-- 用户表
CREATE TABLE `users` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '用户ID',
    `openid` VARCHAR(64) NOT NULL COMMENT '微信openid',
    `nickname` VARCHAR(100) DEFAULT NULL COMMENT '用户昵称',
    `avatar_url` VARCHAR(500) DEFAULT NULL COMMENT '头像URL',
    `gender` TINYINT DEFAULT NULL COMMENT '性别：0-未知，1-男，2-女',
    `city` VARCHAR(50) DEFAULT NULL COMMENT '城市',
    `province` VARCHAR(50) DEFAULT NULL COMMENT '省份',
    `country` VARCHAR(50) DEFAULT NULL COMMENT '国家',
    `is_premium` BOOLEAN DEFAULT FALSE COMMENT '是否为付费用户',
    `premium_expire_time` DATETIME DEFAULT NULL COMMENT '付费到期时间',
    `daily_usage_count` INT DEFAULT 0 COMMENT '今日使用次数',
    `total_usage_count` INT DEFAULT 0 COMMENT '总使用次数',
    `last_usage_time` DATETIME DEFAULT NULL COMMENT '最后使用时间',
    `status` TINYINT DEFAULT 0 COMMENT '状态：0-正常，1-禁用',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_openid` (`openid`),
    KEY `idx_status` (`status`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 语音记录表
CREATE TABLE `voice_records` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '记录ID',
    `voice_id` VARCHAR(64) NOT NULL COMMENT '语音唯一标识',
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `original_filename` VARCHAR(255) NOT NULL COMMENT '原始文件名',
    `stored_filename` VARCHAR(255) NOT NULL COMMENT '存储文件名',
    `file_path` VARCHAR(500) NOT NULL COMMENT '文件路径',
    `file_url` VARCHAR(500) DEFAULT NULL COMMENT '文件访问URL',
    `file_size` BIGINT NOT NULL COMMENT '文件大小（字节）',
    `duration` INT NOT NULL COMMENT '时长（秒）',
    `format` VARCHAR(20) NOT NULL COMMENT '文件格式',
    `voice_features` TEXT DEFAULT NULL COMMENT '语音特征JSON',
    `status` ENUM('UPLOADED', 'PROCESSING', 'COMPLETED', 'FAILED') DEFAULT 'UPLOADED' COMMENT '处理状态',
    `error_message` TEXT DEFAULT NULL COMMENT '错误信息',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_voice_id` (`voice_id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_status` (`status`),
    KEY `idx_create_time` (`create_time`),
    FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='语音记录表';

-- 肖像生成任务表
CREATE TABLE `portrait_tasks` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '任务ID',
    `task_id` VARCHAR(64) NOT NULL COMMENT '任务唯一标识',
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `voice_record_id` BIGINT NOT NULL COMMENT '语音记录ID',
    `art_style` ENUM('REALISTIC', 'CARTOON', 'WATERCOLOR', 'SKETCH') NOT NULL COMMENT '艺术风格',
    `gender_preference` ENUM('AUTO', 'MALE', 'FEMALE', 'NEUTRAL') DEFAULT 'AUTO' COMMENT '性别偏好',
    `color_theme` ENUM('WARM', 'COOL', 'MONOCHROME', 'RAINBOW') DEFAULT 'WARM' COMMENT '色彩主题',
    `background_setting` VARCHAR(200) DEFAULT NULL COMMENT '背景设置',
    `generation_params` TEXT DEFAULT NULL COMMENT '生成参数JSON',
    `status` ENUM('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED') DEFAULT 'PENDING' COMMENT '任务状态',
    `progress` TINYINT DEFAULT 0 COMMENT '进度百分比',
    `estimated_time` INT DEFAULT NULL COMMENT '预计完成时间（秒）',
    `result_image_url` VARCHAR(500) DEFAULT NULL COMMENT '结果图片URL',
    `error_message` TEXT DEFAULT NULL COMMENT '错误信息',
    `start_time` DATETIME DEFAULT NULL COMMENT '开始时间',
    `complete_time` DATETIME DEFAULT NULL COMMENT '完成时间',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_task_id` (`task_id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_voice_record_id` (`voice_record_id`),
    KEY `idx_status` (`status`),
    KEY `idx_create_time` (`create_time`),
    FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
    FOREIGN KEY (`voice_record_id`) REFERENCES `voice_records` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='肖像生成任务表';

-- 作品表
CREATE TABLE `artworks` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '作品ID',
    `artwork_id` VARCHAR(64) NOT NULL COMMENT '作品唯一标识',
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `portrait_task_id` BIGINT NOT NULL COMMENT '肖像任务ID',
    `title` VARCHAR(200) NOT NULL COMMENT '作品标题',
    `description` TEXT DEFAULT NULL COMMENT '作品描述',
    `image_url` VARCHAR(500) NOT NULL COMMENT '图片URL',
    `thumbnail_url` VARCHAR(500) DEFAULT NULL COMMENT '缩略图URL',
    `art_style` ENUM('REALISTIC', 'CARTOON', 'WATERCOLOR', 'SKETCH') NOT NULL COMMENT '艺术风格',
    `gender_preference` ENUM('AUTO', 'MALE', 'FEMALE', 'NEUTRAL') DEFAULT 'AUTO' COMMENT '性别偏好',
    `color_theme` ENUM('WARM', 'COOL', 'MONOCHROME', 'RAINBOW') DEFAULT 'WARM' COMMENT '色彩主题',
    `is_public` BOOLEAN DEFAULT FALSE COMMENT '是否公开',
    `view_count` INT DEFAULT 0 COMMENT '浏览次数',
    `like_count` INT DEFAULT 0 COMMENT '点赞次数',
    `share_count` INT DEFAULT 0 COMMENT '分享次数',
    `status` TINYINT DEFAULT 0 COMMENT '状态：0-正常，1-删除',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_artwork_id` (`artwork_id`),
    UNIQUE KEY `uk_portrait_task_id` (`portrait_task_id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_is_public_status` (`is_public`, `status`),
    KEY `idx_create_time` (`create_time`),
    KEY `idx_like_count` (`like_count`),
    KEY `idx_view_count` (`view_count`),
    FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
    FOREIGN KEY (`portrait_task_id`) REFERENCES `portrait_tasks` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='作品表';

-- 创建索引优化查询性能
CREATE INDEX `idx_users_premium` ON `users` (`is_premium`, `premium_expire_time`);
CREATE INDEX `idx_voice_records_user_status` ON `voice_records` (`user_id`, `status`);
CREATE INDEX `idx_portrait_tasks_user_status` ON `portrait_tasks` (`user_id`, `status`);
CREATE INDEX `idx_artworks_public_time` ON `artworks` (`is_public`, `status`, `create_time`);
CREATE INDEX `idx_artworks_title_search` ON `artworks` (`title`, `is_public`, `status`);
