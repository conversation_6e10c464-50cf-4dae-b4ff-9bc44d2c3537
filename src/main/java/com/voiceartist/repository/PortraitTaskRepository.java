package com.voiceartist.repository;

import com.voiceartist.entity.PortraitTask;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 肖像生成任务数据访问接口
 */
@Repository
public interface PortraitTaskRepository extends JpaRepository<PortraitTask, Long> {

    /**
     * 根据任务ID查找任务
     */
    Optional<PortraitTask> findByTaskId(String taskId);

    /**
     * 根据用户ID查找任务列表
     */
    Page<PortraitTask> findByUserIdOrderByCreateTimeDesc(Long userId, Pageable pageable);

    /**
     * 根据用户ID和状态查找任务
     */
    List<PortraitTask> findByUserIdAndStatus(Long userId, PortraitTask.TaskStatus status);

    /**
     * 查找等待处理的任务
     */
    List<PortraitTask> findByStatusOrderByCreateTimeAsc(PortraitTask.TaskStatus status);

    /**
     * 查找处理中的任务
     */
    @Query("SELECT p FROM PortraitTask p WHERE p.status = :status AND p.startTime IS NOT NULL")
    List<PortraitTask> findProcessingTasks(@Param("status") PortraitTask.TaskStatus status);

    /**
     * 查找超时的处理中任务
     */
    @Query("SELECT p FROM PortraitTask p WHERE p.status = :status AND p.startTime < :timeoutTime")
    List<PortraitTask> findTimeoutTasks(@Param("status") PortraitTask.TaskStatus status,
                                       @Param("timeoutTime") LocalDateTime timeoutTime);

    /**
     * 查询用户今日生成任务数量
     */
    @Query("SELECT COUNT(p) FROM PortraitTask p WHERE p.userId = :userId AND DATE(p.createTime) = CURRENT_DATE")
    long countTodayTasksByUser(@Param("userId") Long userId);

    /**
     * 查询各状态任务数量统计
     */
    @Query("SELECT p.status, COUNT(p) FROM PortraitTask p GROUP BY p.status")
    List<Object[]> countTasksByStatus();

    /**
     * 查询成功率统计
     */
    @Query("SELECT " +
           "SUM(CASE WHEN p.status = 'COMPLETED' THEN 1 ELSE 0 END) as completed, " +
           "SUM(CASE WHEN p.status = 'FAILED' THEN 1 ELSE 0 END) as failed, " +
           "COUNT(p) as total " +
           "FROM PortraitTask p WHERE p.createTime >= :startTime")
    Object[] getSuccessRateStats(@Param("startTime") LocalDateTime startTime);
}
