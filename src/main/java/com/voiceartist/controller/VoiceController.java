package com.voiceartist.controller;

import com.voiceartist.dto.response.ApiResponse;
import com.voiceartist.dto.response.VoiceUploadResponse;
import com.voiceartist.entity.VoiceRecord;
import com.voiceartist.service.VoiceService;
import com.voiceartist.util.FileUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.nio.file.Paths;

/**
 * 语音处理控制器
 */
@Slf4j
@RestController
@RequestMapping("/voice")
@RequiredArgsConstructor
@Tag(name = "语音处理", description = "语音文件上传和处理相关接口")
public class VoiceController {

    private final VoiceService voiceService;

    /**
     * 上传语音文件
     */
    @PostMapping("/upload")
    @Operation(summary = "上传语音文件", description = "上传语音文件并开始处理")
    public ApiResponse<VoiceUploadResponse> uploadVoice(
            @RequestAttribute("userId") Long userId,
            @RequestParam("audioFile") MultipartFile audioFile,
            @RequestParam("duration") Integer duration) {
        
        log.info("语音上传请求: userId={}, filename={}, duration={}s", 
                userId, audioFile.getOriginalFilename(), duration);
        
        VoiceUploadResponse response = voiceService.uploadVoice(userId, audioFile, duration);
        
        log.info("语音上传成功: userId={}, voiceId={}", userId, response.getVoiceId());
        return ApiResponse.success("上传成功", response);
    }

    /**
     * 获取语音处理状态
     */
    @GetMapping("/{voiceId}/status")
    @Operation(summary = "获取语音处理状态", description = "查询语音文件的处理状态和结果")
    public ApiResponse<VoiceStatusResponse> getVoiceStatus(
            @RequestAttribute("userId") Long userId,
            @PathVariable String voiceId) {
        
        log.info("查询语音状态: userId={}, voiceId={}", userId, voiceId);
        
        VoiceRecord voiceRecord = voiceService.getVoiceStatus(voiceId, userId);
        VoiceStatusResponse response = buildVoiceStatusResponse(voiceRecord);
        
        return ApiResponse.success(response);
    }

    /**
     * 下载语音文件
     */
    @GetMapping("/{voiceId}/download")
    @Operation(summary = "下载语音文件", description = "下载已上传的语音文件")
    public ResponseEntity<Resource> downloadVoice(@PathVariable String voiceId) {
        log.info("下载语音文件: voiceId={}", voiceId);
        
        try {
            VoiceRecord voiceRecord = voiceService.getVoiceRecord(voiceId);
            File file = new File(voiceRecord.getFilePath());
            
            if (!file.exists()) {
                return ResponseEntity.notFound().build();
            }
            
            Resource resource = new FileSystemResource(file);
            String mimeType = FileUtil.getMimeType(voiceRecord.getStoredFilename());
            
            return ResponseEntity.ok()
                    .contentType(MediaType.parseMediaType(mimeType))
                    .header(HttpHeaders.CONTENT_DISPOSITION, 
                           "attachment; filename=\"" + voiceRecord.getOriginalFilename() + "\"")
                    .body(resource);
                    
        } catch (Exception e) {
            log.error("下载语音文件失败: voiceId={}, error={}", voiceId, e.getMessage());
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * 构建语音状态响应
     */
    private VoiceStatusResponse buildVoiceStatusResponse(VoiceRecord voiceRecord) {
        VoiceStatusResponse response = new VoiceStatusResponse();
        response.setVoiceId(voiceRecord.getVoiceId());
        response.setStatus(voiceRecord.getStatus());
        response.setDuration(voiceRecord.getDuration());
        response.setFileSize(voiceRecord.getFileSize());
        response.setFormat(voiceRecord.getFormat());
        response.setVoiceFeatures(voiceRecord.getVoiceFeatures());
        response.setErrorMessage(voiceRecord.getErrorMessage());
        response.setCreateTime(voiceRecord.getCreateTime());
        response.setUpdateTime(voiceRecord.getUpdateTime());
        return response;
    }

    /**
     * 语音状态响应DTO
     */
    public static class VoiceStatusResponse {
        private String voiceId;
        private VoiceRecord.ProcessStatus status;
        private Integer duration;
        private Long fileSize;
        private String format;
        private String voiceFeatures;
        private String errorMessage;
        private java.time.LocalDateTime createTime;
        private java.time.LocalDateTime updateTime;

        // Getters and Setters
        public String getVoiceId() { return voiceId; }
        public void setVoiceId(String voiceId) { this.voiceId = voiceId; }
        public VoiceRecord.ProcessStatus getStatus() { return status; }
        public void setStatus(VoiceRecord.ProcessStatus status) { this.status = status; }
        public Integer getDuration() { return duration; }
        public void setDuration(Integer duration) { this.duration = duration; }
        public Long getFileSize() { return fileSize; }
        public void setFileSize(Long fileSize) { this.fileSize = fileSize; }
        public String getFormat() { return format; }
        public void setFormat(String format) { this.format = format; }
        public String getVoiceFeatures() { return voiceFeatures; }
        public void setVoiceFeatures(String voiceFeatures) { this.voiceFeatures = voiceFeatures; }
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
        public java.time.LocalDateTime getCreateTime() { return createTime; }
        public void setCreateTime(java.time.LocalDateTime createTime) { this.createTime = createTime; }
        public java.time.LocalDateTime getUpdateTime() { return updateTime; }
        public void setUpdateTime(java.time.LocalDateTime updateTime) { this.updateTime = updateTime; }
    }
}
