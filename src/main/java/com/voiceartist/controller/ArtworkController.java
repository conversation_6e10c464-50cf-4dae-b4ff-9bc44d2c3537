package com.voiceartist.controller;

import com.voiceartist.dto.response.ApiResponse;
import com.voiceartist.entity.Artwork;
import com.voiceartist.service.ArtworkService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

/**
 * 作品管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/artwork")
@RequiredArgsConstructor
@Tag(name = "作品管理", description = "作品保存、查看、分享等功能")
public class ArtworkController {

    private final ArtworkService artworkService;

    /**
     * 保存作品
     */
    @PostMapping("/save")
    @Operation(summary = "保存作品", description = "将生成的肖像保存为作品")
    public ApiResponse<ArtworkResponse> saveArtwork(
            @RequestAttribute("userId") Long userId,
            @RequestBody SaveArtworkRequest request) {
        
        log.info("保存作品: userId={}, taskId={}", userId, request.getTaskId());
        
        Artwork artwork = artworkService.saveArtwork(userId, request.getTaskId(), 
                                                   request.getTitle(), request.getDescription());
        
        ArtworkResponse response = buildArtworkResponse(artwork);
        
        log.info("作品保存成功: userId={}, artworkId={}", userId, artwork.getArtworkId());
        return ApiResponse.success("保存成功", response);
    }

    /**
     * 获取用户作品列表
     */
    @GetMapping("/my")
    @Operation(summary = "获取我的作品", description = "获取当前用户的所有作品列表")
    public ApiResponse<Page<ArtworkResponse>> getMyArtworks(
            @RequestAttribute("userId") Long userId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        
        log.info("获取用户作品列表: userId={}, page={}, size={}", userId, page, size);
        
        Page<Artwork> artworks = artworkService.getUserArtworks(userId, page, size);
        Page<ArtworkResponse> response = artworks.map(this::buildArtworkResponse);
        
        return ApiResponse.success(response);
    }

    /**
     * 获取作品详情
     */
    @GetMapping("/{artworkId}")
    @Operation(summary = "获取作品详情", description = "获取指定作品的详细信息")
    public ApiResponse<ArtworkResponse> getArtworkDetail(
            @RequestAttribute("userId") Long userId,
            @PathVariable String artworkId) {
        
        log.info("获取作品详情: userId={}, artworkId={}", userId, artworkId);
        
        Artwork artwork = artworkService.getArtworkDetail(artworkId, userId);
        ArtworkResponse response = buildArtworkResponse(artwork);
        
        return ApiResponse.success(response);
    }

    /**
     * 更新作品信息
     */
    @PutMapping("/{artworkId}")
    @Operation(summary = "更新作品信息", description = "更新作品标题、描述和公开状态")
    public ApiResponse<ArtworkResponse> updateArtwork(
            @RequestAttribute("userId") Long userId,
            @PathVariable String artworkId,
            @RequestBody UpdateArtworkRequest request) {
        
        log.info("更新作品信息: userId={}, artworkId={}", userId, artworkId);
        
        Artwork artwork = artworkService.updateArtwork(userId, artworkId, 
                request.getTitle(), request.getDescription(), request.getIsPublic());
        
        ArtworkResponse response = buildArtworkResponse(artwork);
        
        return ApiResponse.success("更新成功", response);
    }

    /**
     * 删除作品
     */
    @DeleteMapping("/{artworkId}")
    @Operation(summary = "删除作品", description = "删除指定的作品")
    public ApiResponse<Void> deleteArtwork(
            @RequestAttribute("userId") Long userId,
            @PathVariable String artworkId) {
        
        log.info("删除作品: userId={}, artworkId={}", userId, artworkId);
        
        artworkService.deleteArtwork(userId, artworkId);
        
        return ApiResponse.success("删除成功");
    }

    /**
     * 点赞作品
     */
    @PostMapping("/{artworkId}/like")
    @Operation(summary = "点赞作品", description = "为公开作品点赞")
    public ApiResponse<Void> likeArtwork(
            @RequestAttribute("userId") Long userId,
            @PathVariable String artworkId) {
        
        log.info("点赞作品: userId={}, artworkId={}", userId, artworkId);
        
        artworkService.likeArtwork(userId, artworkId);
        
        return ApiResponse.success("点赞成功");
    }

    /**
     * 分享作品
     */
    @PostMapping("/{artworkId}/share")
    @Operation(summary = "分享作品", description = "分享公开作品")
    public ApiResponse<Void> shareArtwork(
            @RequestAttribute("userId") Long userId,
            @PathVariable String artworkId) {
        
        log.info("分享作品: userId={}, artworkId={}", userId, artworkId);
        
        artworkService.shareArtwork(userId, artworkId);
        
        return ApiResponse.success("分享成功");
    }

    /**
     * 获取公开作品列表（广场）
     */
    @GetMapping("/public")
    @Operation(summary = "获取公开作品", description = "获取所有公开作品列表")
    public ApiResponse<Page<ArtworkResponse>> getPublicArtworks(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "latest") String sortBy) {
        
        log.info("获取公开作品列表: page={}, size={}, sortBy={}", page, size, sortBy);
        
        Page<Artwork> artworks = artworkService.getPublicArtworks(page, size, sortBy);
        Page<ArtworkResponse> response = artworks.map(this::buildArtworkResponse);
        
        return ApiResponse.success(response);
    }

    /**
     * 搜索公开作品
     */
    @GetMapping("/search")
    @Operation(summary = "搜索作品", description = "根据关键词搜索公开作品")
    public ApiResponse<Page<ArtworkResponse>> searchArtworks(
            @RequestParam String keyword,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        
        log.info("搜索作品: keyword={}, page={}, size={}", keyword, page, size);
        
        Page<Artwork> artworks = artworkService.searchPublicArtworks(keyword, page, size);
        Page<ArtworkResponse> response = artworks.map(this::buildArtworkResponse);
        
        return ApiResponse.success(response);
    }

    /**
     * 获取用户统计信息
     */
    @GetMapping("/stats")
    @Operation(summary = "获取作品统计", description = "获取当前用户的作品统计信息")
    public ApiResponse<ArtworkService.UserArtworkStats> getUserStats(
            @RequestAttribute("userId") Long userId) {
        
        log.info("获取用户作品统计: userId={}", userId);
        
        ArtworkService.UserArtworkStats stats = artworkService.getUserStats(userId);
        
        return ApiResponse.success(stats);
    }

    /**
     * 构建作品响应
     */
    private ArtworkResponse buildArtworkResponse(Artwork artwork) {
        ArtworkResponse response = new ArtworkResponse();
        response.setArtworkId(artwork.getArtworkId());
        response.setTitle(artwork.getTitle());
        response.setDescription(artwork.getDescription());
        response.setImageUrl(artwork.getImageUrl());
        response.setThumbnailUrl(artwork.getThumbnailUrl());
        response.setArtStyle(artwork.getArtStyle());
        response.setGenderPreference(artwork.getGenderPreference());
        response.setColorTheme(artwork.getColorTheme());
        response.setIsPublic(artwork.getIsPublic());
        response.setViewCount(artwork.getViewCount());
        response.setLikeCount(artwork.getLikeCount());
        response.setShareCount(artwork.getShareCount());
        response.setCreateTime(artwork.getCreateTime());
        response.setUpdateTime(artwork.getUpdateTime());
        return response;
    }

    /**
     * 保存作品请求DTO
     */
    public static class SaveArtworkRequest {
        private String taskId;
        private String title;
        private String description;

        public String getTaskId() { return taskId; }
        public void setTaskId(String taskId) { this.taskId = taskId; }
        public String getTitle() { return title; }
        public void setTitle(String title) { this.title = title; }
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
    }

    /**
     * 更新作品请求DTO
     */
    public static class UpdateArtworkRequest {
        private String title;
        private String description;
        private Boolean isPublic;

        public String getTitle() { return title; }
        public void setTitle(String title) { this.title = title; }
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        public Boolean getIsPublic() { return isPublic; }
        public void setIsPublic(Boolean isPublic) { this.isPublic = isPublic; }
    }

    /**
     * 作品响应DTO
     */
    public static class ArtworkResponse {
        private String artworkId;
        private String title;
        private String description;
        private String imageUrl;
        private String thumbnailUrl;
        private com.voiceartist.entity.PortraitTask.ArtStyle artStyle;
        private com.voiceartist.entity.PortraitTask.GenderPreference genderPreference;
        private com.voiceartist.entity.PortraitTask.ColorTheme colorTheme;
        private Boolean isPublic;
        private Integer viewCount;
        private Integer likeCount;
        private Integer shareCount;
        private java.time.LocalDateTime createTime;
        private java.time.LocalDateTime updateTime;

        // Getters and Setters
        public String getArtworkId() { return artworkId; }
        public void setArtworkId(String artworkId) { this.artworkId = artworkId; }
        public String getTitle() { return title; }
        public void setTitle(String title) { this.title = title; }
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        public String getImageUrl() { return imageUrl; }
        public void setImageUrl(String imageUrl) { this.imageUrl = imageUrl; }
        public String getThumbnailUrl() { return thumbnailUrl; }
        public void setThumbnailUrl(String thumbnailUrl) { this.thumbnailUrl = thumbnailUrl; }
        public com.voiceartist.entity.PortraitTask.ArtStyle getArtStyle() { return artStyle; }
        public void setArtStyle(com.voiceartist.entity.PortraitTask.ArtStyle artStyle) { this.artStyle = artStyle; }
        public com.voiceartist.entity.PortraitTask.GenderPreference getGenderPreference() { return genderPreference; }
        public void setGenderPreference(com.voiceartist.entity.PortraitTask.GenderPreference genderPreference) { this.genderPreference = genderPreference; }
        public com.voiceartist.entity.PortraitTask.ColorTheme getColorTheme() { return colorTheme; }
        public void setColorTheme(com.voiceartist.entity.PortraitTask.ColorTheme colorTheme) { this.colorTheme = colorTheme; }
        public Boolean getIsPublic() { return isPublic; }
        public void setIsPublic(Boolean isPublic) { this.isPublic = isPublic; }
        public Integer getViewCount() { return viewCount; }
        public void setViewCount(Integer viewCount) { this.viewCount = viewCount; }
        public Integer getLikeCount() { return likeCount; }
        public void setLikeCount(Integer likeCount) { this.likeCount = likeCount; }
        public Integer getShareCount() { return shareCount; }
        public void setShareCount(Integer shareCount) { this.shareCount = shareCount; }
        public java.time.LocalDateTime getCreateTime() { return createTime; }
        public void setCreateTime(java.time.LocalDateTime createTime) { this.createTime = createTime; }
        public java.time.LocalDateTime getUpdateTime() { return updateTime; }
        public void setUpdateTime(java.time.LocalDateTime updateTime) { this.updateTime = updateTime; }
    }
}
