# 语音肖像生成系统后端API

基于语音特征生成个性化肖像画的微信小程序后端服务，使用Spring Boot 3.2.12 + Java 17开发。

## 项目概述

本项目为语音肖像生成微信小程序提供后端API服务，用户可以通过录制10-30秒的语音，系统会分析语音特征并生成个性化的肖像画作品。

### 核心功能

- **用户管理**：微信小程序登录、用户信息管理、使用配额控制
- **语音处理**：语音文件上传、格式验证、特征提取
- **AI肖像生成**：基于语音特征的异步肖像生成
- **作品管理**：作品保存、查看、分享、社交互动
- **系统监控**：健康检查、系统信息、业务配置

## 技术栈

- **框架**：Spring Boot 3.2.12
- **语言**：Java 17
- **构建工具**：Maven
- **数据库**：MySQL 8.0 + Redis
- **认证**：JWT + 微信小程序登录
- **文档**：Swagger/OpenAPI 3
- **数据库迁移**：Flyway

## 项目结构

```
src/main/java/com/voiceartist/
├── VoicePortraitApplication.java          # 主启动类
├── config/                                # 配置类
│   ├── SecurityConfig.java               # Spring Security配置
│   └── SwaggerConfig.java                # Swagger文档配置
├── controller/                            # 控制器层
│   ├── AuthController.java               # 认证接口
│   ├── UserController.java               # 用户管理
│   ├── VoiceController.java              # 语音处理
│   ├── PortraitController.java           # 肖像生成
│   ├── ArtworkController.java            # 作品管理
│   └── SystemController.java             # 系统管理
├── service/                               # 服务层
│   ├── UserService.java                  # 用户服务
│   ├── VoiceService.java                 # 语音服务
│   ├── PortraitService.java              # 肖像生成服务
│   └── ArtworkService.java               # 作品服务
├── repository/                            # 数据访问层
│   ├── UserRepository.java               # 用户数据访问
│   ├── VoiceRecordRepository.java        # 语音记录数据访问
│   ├── PortraitTaskRepository.java       # 肖像任务数据访问
│   └── ArtworkRepository.java            # 作品数据访问
├── entity/                                # 实体类
│   ├── User.java                         # 用户实体
│   ├── VoiceRecord.java                  # 语音记录实体
│   ├── PortraitTask.java                 # 肖像任务实体
│   └── Artwork.java                      # 作品实体
├── dto/                                   # 数据传输对象
│   ├── request/                          # 请求DTO
│   └── response/                         # 响应DTO
├── exception/                             # 异常处理
│   ├── BusinessException.java            # 业务异常
│   └── GlobalExceptionHandler.java       # 全局异常处理器
├── filter/                                # 过滤器
│   └── JwtAuthenticationFilter.java      # JWT认证过滤器
└── util/                                  # 工具类
    ├── JwtUtil.java                      # JWT工具
    └── FileUtil.java                     # 文件工具
```

## API接口

### 认证模块
- `POST /auth/login` - 微信小程序登录

### 用户管理
- `GET /user/profile` - 获取用户信息
- `PUT /user/profile` - 更新用户信息
- `GET /user/quota` - 检查使用配额

### 语音处理
- `POST /voice/upload` - 上传语音文件
- `GET /voice/{voiceId}/status` - 获取处理状态
- `GET /voice/{voiceId}/download` - 下载语音文件

### 肖像生成
- `POST /portrait/generate` - 创建生成任务
- `GET /portrait/task/{taskId}` - 查询任务状态
- `POST /portrait/regenerate` - 重新生成

### 作品管理
- `POST /artwork/save` - 保存作品
- `GET /artwork/my` - 获取我的作品
- `GET /artwork/{artworkId}` - 获取作品详情
- `PUT /artwork/{artworkId}` - 更新作品信息
- `DELETE /artwork/{artworkId}` - 删除作品
- `POST /artwork/{artworkId}/like` - 点赞作品
- `POST /artwork/{artworkId}/share` - 分享作品
- `GET /artwork/public` - 获取公开作品
- `GET /artwork/search` - 搜索作品
- `GET /artwork/stats` - 获取统计信息

### 系统管理
- `GET /system/health` - 健康检查
- `GET /system/info` - 系统信息
- `GET /system/config` - 业务配置

## 数据库设计

### 核心表结构
- `users` - 用户表
- `voice_records` - 语音记录表
- `portrait_tasks` - 肖像生成任务表
- `artworks` - 作品表

## 配置说明

### application.yml 主要配置项

```yaml
# 数据库配置
spring:
  datasource:
    url: ******************************************
    username: root
    password: password

# Redis配置
  data:
    redis:
      host: localhost
      port: 6379

# 微信小程序配置
wechat:
  miniapp:
    app-id: your-app-id
    app-secret: your-app-secret

# JWT配置
jwt:
  secret: your-jwt-secret
  expiration: 86400

# 文件上传配置
file:
  upload:
    voice-path: /data/voice/
    image-path: /data/images/
    max-size: 52428800

# 业务规则配置
business:
  voice:
    min-duration: 10
    max-duration: 30
  quota:
    free-daily-limit: 3
    premium-daily-limit: 100
```

## 快速开始

### 环境要求
- Java 17+
- Maven 3.6+
- MySQL 8.0+
- Redis 6.0+

### 运行步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd voice-artist
```

2. **配置数据库**
```sql
CREATE DATABASE voice_portrait CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

3. **修改配置**
编辑 `src/main/resources/application.yml`，配置数据库连接、Redis、微信小程序等信息。

4. **安装依赖**
```bash
mvn clean install
```

5. **运行应用**
```bash
mvn spring-boot:run
```

6. **访问文档**
启动后访问：http://localhost:8080/swagger-ui.html

## 部署说明

### Docker部署
```dockerfile
FROM openjdk:17-jdk-slim
COPY target/voice-artist-1.0.0.jar app.jar
EXPOSE 8080
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

### 生产环境配置
- 配置生产数据库连接
- 设置Redis集群
- 配置文件存储路径
- 设置JWT密钥
- 配置微信小程序信息
- 设置日志级别和路径

## 开发规范

### 代码规范
- 使用Java 17语法特性
- 遵循Spring Boot最佳实践
- 统一异常处理
- 完善的日志记录
- 接口文档完整

### 数据库规范
- 统一字段命名
- 合理的索引设计
- 外键约束
- 软删除机制

## 许可证

MIT License
