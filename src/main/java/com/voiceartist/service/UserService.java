package com.voiceartist.service;

import com.voiceartist.dto.request.LoginRequest;
import com.voiceartist.dto.response.LoginResponse;
import com.voiceartist.entity.User;
import com.voiceartist.exception.BusinessException;
import com.voiceartist.repository.UserRepository;
import com.voiceartist.util.JwtUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.reactive.function.client.WebClient;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.Optional;

/**
 * 用户服务类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserService {

    private final UserRepository userRepository;
    private final JwtUtil jwtUtil;
    private final WebClient.Builder webClientBuilder;

    @Value("${wechat.miniapp.app-id}")
    private String appId;

    @Value("${wechat.miniapp.app-secret}")
    private String appSecret;

    /**
     * 微信小程序登录
     */
    @Transactional
    public LoginResponse login(LoginRequest request) {
        try {
            // 1. 调用微信API获取openid
            String openid = getOpenidFromWechat(request.getCode());
            
            // 2. 查找或创建用户
            User user = findOrCreateUser(openid, request.getUserInfo());
            
            // 3. 生成JWT token
            String token = jwtUtil.generateToken(user.getId().toString(), user.getOpenid());
            
            // 4. 构建响应
            return buildLoginResponse(token, user);
            
        } catch (Exception e) {
            log.error("用户登录失败: {}", e.getMessage(), e);
            throw new BusinessException("登录失败，请重试");
        }
    }

    /**
     * 根据用户ID获取用户信息
     */
    public User getUserById(Long userId) {
        return userRepository.findById(userId)
                .orElseThrow(() -> new BusinessException("用户不存在"));
    }

    /**
     * 根据openid获取用户信息
     */
    public Optional<User> getUserByOpenid(String openid) {
        return userRepository.findByOpenidAndStatus(openid, 0);
    }

    /**
     * 更新用户信息
     */
    @Transactional
    public User updateUserInfo(Long userId, String nickname, String avatarUrl) {
        User user = getUserById(userId);
        
        if (nickname != null && !nickname.trim().isEmpty()) {
            user.setNickname(nickname.trim());
        }
        
        if (avatarUrl != null && !avatarUrl.trim().isEmpty()) {
            user.setAvatarUrl(avatarUrl.trim());
        }
        
        return userRepository.save(user);
    }

    /**
     * 增加用户使用次数
     */
    @Transactional
    public void incrementUsageCount(Long userId) {
        User user = getUserById(userId);
        
        // 检查使用配额
        if (!user.hasUsageQuota()) {
            throw new BusinessException("今日使用次数已达上限");
        }
        
        // 更新使用次数
        int updated = userRepository.incrementUsageCount(userId, LocalDateTime.now());
        if (updated == 0) {
            throw new BusinessException("更新使用次数失败");
        }
    }

    /**
     * 检查用户是否有使用配额
     */
    public boolean checkUsageQuota(Long userId) {
        User user = getUserById(userId);
        return user.hasUsageQuota();
    }

    /**
     * 从微信获取openid
     */
    private String getOpenidFromWechat(String code) {
        try {
            WebClient webClient = webClientBuilder.build();
            
            Map<String, Object> response = webClient.get()
                    .uri("https://api.weixin.qq.com/sns/jscode2session" +
                         "?appid={appid}&secret={secret}&js_code={code}&grant_type=authorization_code",
                         appId, appSecret, code)
                    .retrieve()
                    .bodyToMono(Map.class)
                    .block();
            
            if (response == null || response.containsKey("errcode")) {
                String errorMsg = response != null ? (String) response.get("errmsg") : "获取用户信息失败";
                throw new BusinessException("微信登录失败: " + errorMsg);
            }
            
            return (String) response.get("openid");
            
        } catch (Exception e) {
            log.error("调用微信API失败: {}", e.getMessage(), e);
            throw new BusinessException("微信登录失败，请重试");
        }
    }

    /**
     * 查找或创建用户
     */
    private User findOrCreateUser(String openid, LoginRequest.WechatUserInfo userInfo) {
        Optional<User> existingUser = userRepository.findByOpenid(openid);
        
        if (existingUser.isPresent()) {
            // 更新用户信息
            User user = existingUser.get();
            if (userInfo != null) {
                updateUserFromWechatInfo(user, userInfo);
                return userRepository.save(user);
            }
            return user;
        } else {
            // 创建新用户
            User newUser = new User();
            newUser.setOpenid(openid);
            
            if (userInfo != null) {
                updateUserFromWechatInfo(newUser, userInfo);
            }
            
            return userRepository.save(newUser);
        }
    }

    /**
     * 从微信用户信息更新用户数据
     */
    private void updateUserFromWechatInfo(User user, LoginRequest.WechatUserInfo userInfo) {
        if (userInfo.getNickName() != null) {
            user.setNickname(userInfo.getNickName());
        }
        if (userInfo.getAvatarUrl() != null) {
            user.setAvatarUrl(userInfo.getAvatarUrl());
        }
        if (userInfo.getGender() != null) {
            user.setGender(userInfo.getGender());
        }
        if (userInfo.getCity() != null) {
            user.setCity(userInfo.getCity());
        }
        if (userInfo.getProvince() != null) {
            user.setProvince(userInfo.getProvince());
        }
        if (userInfo.getCountry() != null) {
            user.setCountry(userInfo.getCountry());
        }
    }

    /**
     * 构建登录响应
     */
    private LoginResponse buildLoginResponse(String token, User user) {
        LoginResponse response = new LoginResponse();
        response.setToken(token);
        response.setUserId(user.getId().toString());
        
        LoginResponse.UserInfo userInfo = new LoginResponse.UserInfo();
        userInfo.setNickname(user.getNickname());
        userInfo.setAvatarUrl(user.getAvatarUrl());
        userInfo.setIsPremium(user.isValidPremium());
        userInfo.setPremiumExpireTime(user.getPremiumExpireTime());
        userInfo.setDailyUsageCount(user.getDailyUsageCount());
        userInfo.setTotalUsageCount(user.getTotalUsageCount());
        userInfo.setCreateTime(user.getCreateTime());
        
        response.setUserInfo(userInfo);
        return response;
    }
}
