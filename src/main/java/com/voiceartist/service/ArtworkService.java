package com.voiceartist.service;

import com.voiceartist.entity.Artwork;
import com.voiceartist.entity.PortraitTask;
import com.voiceartist.exception.BusinessException;
import com.voiceartist.repository.ArtworkRepository;
import com.voiceartist.repository.PortraitTaskRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * 作品管理服务类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ArtworkService {

    private final ArtworkRepository artworkRepository;
    private final PortraitTaskRepository portraitTaskRepository;

    /**
     * 保存作品
     */
    @Transactional
    public Artwork saveArtwork(Long userId, String taskId, String title, String description) {
        try {
            // 1. 验证任务
            PortraitTask task = portraitTaskRepository.findByTaskId(taskId)
                    .orElseThrow(() -> new BusinessException("任务不存在"));

            if (!task.getUserId().equals(userId)) {
                throw new BusinessException("无权访问该任务");
            }

            if (task.getStatus() != PortraitTask.TaskStatus.COMPLETED) {
                throw new BusinessException("任务未完成，无法保存作品");
            }

            // 2. 检查是否已保存
            Optional<Artwork> existingArtwork = artworkRepository.findByPortraitTaskId(task.getId());
            if (existingArtwork.isPresent()) {
                throw new BusinessException("该作品已保存");
            }

            // 3. 创建作品记录
            Artwork artwork = new Artwork();
            artwork.setArtworkId(UUID.randomUUID().toString());
            artwork.setUserId(userId);
            artwork.setPortraitTaskId(task.getId());
            artwork.setTitle(title != null ? title : "我的语音肖像");
            artwork.setDescription(description);
            artwork.setImageUrl(task.getResultImageUrl());
            artwork.setThumbnailUrl(task.getResultImageUrl() + "?thumbnail=true");
            artwork.setArtStyle(task.getArtStyle());
            artwork.setGenderPreference(task.getGenderPreference());
            artwork.setColorTheme(task.getColorTheme());
            artwork.setIsPublic(false); // 默认私有
            artwork.setViewCount(0);
            artwork.setLikeCount(0);
            artwork.setShareCount(0);

            return artworkRepository.save(artwork);

        } catch (Exception e) {
            log.error("保存作品失败: userId={}, taskId={}, error={}", userId, taskId, e.getMessage(), e);
            if (e instanceof BusinessException) {
                throw e;
            }
            throw new BusinessException("保存作品失败，请重试");
        }
    }

    /**
     * 获取用户作品列表
     */
    public Page<Artwork> getUserArtworks(Long userId, int page, int size) {
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "createTime"));
        return artworkRepository.findByUserIdAndStatus(userId, 0, pageable);
    }

    /**
     * 获取作品详情
     */
    @Transactional
    public Artwork getArtworkDetail(String artworkId, Long userId) {
        Artwork artwork = artworkRepository.findByArtworkId(artworkId)
                .orElseThrow(() -> new BusinessException("作品不存在"));

        // 如果是公开作品或者是作者本人，允许查看
        if (!artwork.getIsPublic() && !artwork.getUserId().equals(userId)) {
            throw new BusinessException("无权访问该作品");
        }

        // 增加浏览次数（非作者本人）
        if (!artwork.getUserId().equals(userId)) {
            artworkRepository.incrementViewCount(artwork.getId());
            artwork.setViewCount(artwork.getViewCount() + 1);
        }

        return artwork;
    }

    /**
     * 更新作品信息
     */
    @Transactional
    public Artwork updateArtwork(Long userId, String artworkId, String title, 
                               String description, Boolean isPublic) {
        Artwork artwork = artworkRepository.findByArtworkId(artworkId)
                .orElseThrow(() -> new BusinessException("作品不存在"));

        // 验证权限
        if (!artwork.getUserId().equals(userId)) {
            throw new BusinessException("无权修改该作品");
        }

        // 更新信息
        if (title != null && !title.trim().isEmpty()) {
            artwork.setTitle(title.trim());
        }
        
        if (description != null) {
            artwork.setDescription(description.trim());
        }
        
        if (isPublic != null) {
            artwork.setIsPublic(isPublic);
        }

        return artworkRepository.save(artwork);
    }

    /**
     * 删除作品
     */
    @Transactional
    public void deleteArtwork(Long userId, String artworkId) {
        Artwork artwork = artworkRepository.findByArtworkId(artworkId)
                .orElseThrow(() -> new BusinessException("作品不存在"));

        // 验证权限
        if (!artwork.getUserId().equals(userId)) {
            throw new BusinessException("无权删除该作品");
        }

        // 软删除
        artwork.setStatus(1);
        artwork.setUpdateTime(LocalDateTime.now());
        artworkRepository.save(artwork);

        log.info("作品删除成功: userId={}, artworkId={}", userId, artworkId);
    }

    /**
     * 点赞作品
     */
    @Transactional
    public void likeArtwork(Long userId, String artworkId) {
        Artwork artwork = artworkRepository.findByArtworkId(artworkId)
                .orElseThrow(() -> new BusinessException("作品不存在"));

        if (!artwork.getIsPublic()) {
            throw new BusinessException("该作品未公开");
        }

        // 检查是否已点赞（这里简化处理，实际应该有单独的点赞记录表）
        // TODO: 实现点赞记录表和去重逻辑

        artworkRepository.incrementLikeCount(artwork.getId());
        log.info("作品点赞成功: userId={}, artworkId={}", userId, artworkId);
    }

    /**
     * 分享作品
     */
    @Transactional
    public void shareArtwork(Long userId, String artworkId) {
        Artwork artwork = artworkRepository.findByArtworkId(artworkId)
                .orElseThrow(() -> new BusinessException("作品不存在"));

        if (!artwork.getIsPublic()) {
            throw new BusinessException("该作品未公开");
        }

        artworkRepository.incrementShareCount(artwork.getId());
        log.info("作品分享成功: userId={}, artworkId={}", userId, artworkId);
    }

    /**
     * 获取公开作品列表（广场）
     */
    public Page<Artwork> getPublicArtworks(int page, int size, String sortBy) {
        Sort sort = switch (sortBy) {
            case "popular" -> Sort.by(Sort.Direction.DESC, "likeCount", "viewCount");
            case "latest" -> Sort.by(Sort.Direction.DESC, "createTime");
            case "trending" -> Sort.by(Sort.Direction.DESC, "shareCount", "likeCount");
            default -> Sort.by(Sort.Direction.DESC, "createTime");
        };

        Pageable pageable = PageRequest.of(page, size, sort);
        return artworkRepository.findByIsPublicAndStatus(true, 0, pageable);
    }

    /**
     * 搜索公开作品
     */
    public Page<Artwork> searchPublicArtworks(String keyword, int page, int size) {
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "createTime"));
        return artworkRepository.findByTitleContainingAndIsPublicAndStatus(keyword, true, 0, pageable);
    }

    /**
     * 获取用户统计信息
     */
    public UserArtworkStats getUserStats(Long userId) {
        List<Object[]> stats = artworkRepository.getUserArtworkStats(userId);
        
        UserArtworkStats userStats = new UserArtworkStats();
        if (!stats.isEmpty()) {
            Object[] result = stats.get(0);
            userStats.setTotalArtworks(((Number) result[0]).intValue());
            userStats.setPublicArtworks(((Number) result[1]).intValue());
            userStats.setTotalViews(((Number) result[2]).longValue());
            userStats.setTotalLikes(((Number) result[3]).longValue());
            userStats.setTotalShares(((Number) result[4]).longValue());
        }
        
        return userStats;
    }

    /**
     * 用户作品统计DTO
     */
    public static class UserArtworkStats {
        private Integer totalArtworks = 0;
        private Integer publicArtworks = 0;
        private Long totalViews = 0L;
        private Long totalLikes = 0L;
        private Long totalShares = 0L;

        // Getters and Setters
        public Integer getTotalArtworks() { return totalArtworks; }
        public void setTotalArtworks(Integer totalArtworks) { this.totalArtworks = totalArtworks; }
        public Integer getPublicArtworks() { return publicArtworks; }
        public void setPublicArtworks(Integer publicArtworks) { this.publicArtworks = publicArtworks; }
        public Long getTotalViews() { return totalViews; }
        public void setTotalViews(Long totalViews) { this.totalViews = totalViews; }
        public Long getTotalLikes() { return totalLikes; }
        public void setTotalLikes(Long totalLikes) { this.totalLikes = totalLikes; }
        public Long getTotalShares() { return totalShares; }
        public void setTotalShares(Long totalShares) { this.totalShares = totalShares; }
    }
}
