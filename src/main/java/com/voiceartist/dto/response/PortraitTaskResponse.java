package com.voiceartist.dto.response;

import com.voiceartist.entity.PortraitTask;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 肖像生成任务响应DTO
 */
@Data
public class PortraitTaskResponse {

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 任务状态
     */
    private PortraitTask.TaskStatus status;

    /**
     * 处理进度（0-100）
     */
    private Integer progress;

    /**
     * 预计完成时间（秒）
     */
    private Integer estimatedTime;

    /**
     * 生成结果
     */
    private GenerationResult result;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 完成时间
     */
    private LocalDateTime completeTime;

    @Data
    public static class GenerationResult {
        /**
         * 生成的图片URL
         */
        private String imageUrl;

        /**
         * 缩略图URL
         */
        private String thumbnailUrl;

        /**
         * 图片宽度
         */
        private Integer imageWidth;

        /**
         * 图片高度
         */
        private Integer imageHeight;

        /**
         * 文件大小（字节）
         */
        private Long fileSize;

        /**
         * 艺术风格
         */
        private PortraitTask.ArtStyle artStyle;

        /**
         * 性别偏好
         */
        private PortraitTask.GenderPreference genderPreference;

        /**
         * 色彩主题
         */
        private PortraitTask.ColorTheme colorTheme;
    }
}
