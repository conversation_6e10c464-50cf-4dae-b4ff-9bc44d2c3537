package com.voiceartist.repository;

import com.voiceartist.entity.VoiceRecord;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 语音记录数据访问接口
 */
@Repository
public interface VoiceRecordRepository extends JpaRepository<VoiceRecord, Long> {

    /**
     * 根据语音ID查找记录
     */
    Optional<VoiceRecord> findByVoiceId(String voiceId);

    /**
     * 根据用户ID查找语音记录
     */
    Page<VoiceRecord> findByUserIdOrderByCreateTimeDesc(Long userId, Pageable pageable);

    /**
     * 根据用户ID和状态查找语音记录
     */
    List<VoiceRecord> findByUserIdAndStatus(Long userId, VoiceRecord.ProcessStatus status);

    /**
     * 查找指定时间之前的失败记录（用于清理）
     */
    @Query("SELECT v FROM VoiceRecord v WHERE v.status = :status AND v.createTime < :beforeTime")
    List<VoiceRecord> findFailedRecordsBefore(@Param("status") VoiceRecord.ProcessStatus status,
                                             @Param("beforeTime") LocalDateTime beforeTime);

    /**
     * 查询用户今日上传的语音数量
     */
    @Query("SELECT COUNT(v) FROM VoiceRecord v WHERE v.userId = :userId AND DATE(v.createTime) = CURRENT_DATE")
    long countTodayUploadsByUser(@Param("userId") Long userId);

    /**
     * 查询处理中的任务数量
     */
    long countByStatus(VoiceRecord.ProcessStatus status);

    /**
     * 删除指定时间之前的记录
     */
    void deleteByCreateTimeBefore(LocalDateTime beforeTime);
}
