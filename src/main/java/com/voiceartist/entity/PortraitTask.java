package com.voiceartist.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;

/**
 * 肖像生成任务实体类
 */
@Data
@Entity
@Table(name = "portrait_tasks")
@EqualsAndHashCode(callSuper = false)
public class PortraitTask {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 任务唯一标识
     */
    @Column(name = "task_id", unique = true, nullable = false)
    private String taskId;

    /**
     * 用户ID
     */
    @Column(name = "user_id", nullable = false)
    private Long userId;

    /**
     * 语音记录ID
     */
    @Column(name = "voice_record_id", nullable = false)
    private Long voiceRecordId;

    /**
     * 艺术风格：REALISTIC-写实，CARTOON-卡通，WATERCOLOR-水彩，SKETCH-素描
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "art_style", nullable = false)
    private ArtStyle artStyle;

    /**
     * 性别偏好：AUTO-自动识别，MALE-男性，FEMALE-女性，NEUTRAL-中性
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "gender_preference", nullable = false)
    private GenderPreference genderPreference;

    /**
     * 色彩主题：WARM-暖色调，COOL-冷色调，MONOCHROME-黑白，RAINBOW-彩虹
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "color_theme", nullable = false)
    private ColorTheme colorTheme;

    /**
     * 背景设置
     */
    @Column(name = "background_setting")
    private String backgroundSetting;

    /**
     * 任务状态：PENDING-等待中，PROCESSING-处理中，COMPLETED-完成，FAILED-失败
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private TaskStatus status = TaskStatus.PENDING;

    /**
     * 处理进度（0-100）
     */
    @Column(name = "progress", nullable = false, columnDefinition = "INT DEFAULT 0")
    private Integer progress = 0;

    /**
     * 预计完成时间（秒）
     */
    @Column(name = "estimated_time")
    private Integer estimatedTime;

    /**
     * 生成的图片URL
     */
    @Column(name = "result_image_url")
    private String resultImageUrl;

    /**
     * 生成参数JSON
     */
    @Column(name = "generation_params", columnDefinition = "TEXT")
    private String generationParams;

    /**
     * 错误信息
     */
    @Column(name = "error_message")
    private String errorMessage;

    /**
     * 开始处理时间
     */
    @Column(name = "start_time")
    private LocalDateTime startTime;

    /**
     * 完成时间
     */
    @Column(name = "complete_time")
    private LocalDateTime completeTime;

    /**
     * 创建时间
     */
    @CreationTimestamp
    @Column(name = "create_time", nullable = false, updatable = false)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @UpdateTimestamp
    @Column(name = "update_time", nullable = false)
    private LocalDateTime updateTime;

    /**
     * 艺术风格枚举
     */
    public enum ArtStyle {
        REALISTIC("写实风格"),
        CARTOON("卡通风格"),
        WATERCOLOR("水彩风格"),
        SKETCH("素描风格");

        private final String description;

        ArtStyle(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 性别偏好枚举
     */
    public enum GenderPreference {
        AUTO("自动识别"),
        MALE("男性"),
        FEMALE("女性"),
        NEUTRAL("中性");

        private final String description;

        GenderPreference(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 色彩主题枚举
     */
    public enum ColorTheme {
        WARM("暖色调"),
        COOL("冷色调"),
        MONOCHROME("黑白"),
        RAINBOW("彩虹");

        private final String description;

        ColorTheme(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 任务状态枚举
     */
    public enum TaskStatus {
        PENDING("等待中"),
        PROCESSING("处理中"),
        COMPLETED("完成"),
        FAILED("失败");

        private final String description;

        TaskStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
