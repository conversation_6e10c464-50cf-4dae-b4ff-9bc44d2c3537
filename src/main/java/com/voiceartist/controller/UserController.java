package com.voiceartist.controller;

import com.voiceartist.dto.response.ApiResponse;
import com.voiceartist.entity.User;
import com.voiceartist.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 用户控制器
 */
@Slf4j
@RestController
@RequestMapping("/user")
@RequiredArgsConstructor
@Tag(name = "用户管理", description = "用户信息管理相关接口")
public class UserController {

    private final UserService userService;

    /**
     * 获取用户信息
     */
    @GetMapping("/profile")
    @Operation(summary = "获取用户信息", description = "获取当前登录用户的详细信息")
    public ApiResponse<UserProfileResponse> getProfile(@RequestAttribute("userId") Long userId) {
        log.info("获取用户信息: userId={}", userId);
        
        User user = userService.getUserById(userId);
        UserProfileResponse response = buildUserProfileResponse(user);
        
        return ApiResponse.success(response);
    }

    /**
     * 更新用户信息
     */
    @PutMapping("/profile")
    @Operation(summary = "更新用户信息", description = "更新用户昵称和头像")
    public ApiResponse<UserProfileResponse> updateProfile(
            @RequestAttribute("userId") Long userId,
            @RequestBody UpdateProfileRequest request) {
        
        log.info("更新用户信息: userId={}, nickname={}", userId, request.getNickname());
        
        User user = userService.updateUserInfo(userId, request.getNickname(), request.getAvatarUrl());
        UserProfileResponse response = buildUserProfileResponse(user);
        
        return ApiResponse.success("更新成功", response);
    }

    /**
     * 检查使用配额
     */
    @GetMapping("/quota")
    @Operation(summary = "检查使用配额", description = "检查用户今日剩余使用次数")
    public ApiResponse<QuotaResponse> checkQuota(@RequestAttribute("userId") Long userId) {
        log.info("检查用户配额: userId={}", userId);
        
        User user = userService.getUserById(userId);
        QuotaResponse response = new QuotaResponse();
        response.setDailyLimit(user.isValidPremium() ? 100 : 3);
        response.setUsedToday(user.getDailyUsageCount());
        response.setRemainingToday(response.getDailyLimit() - response.getUsedToday());
        response.setIsPremium(user.isValidPremium());
        response.setPremiumExpireTime(user.getPremiumExpireTime());
        
        return ApiResponse.success(response);
    }

    /**
     * 构建用户信息响应
     */
    private UserProfileResponse buildUserProfileResponse(User user) {
        UserProfileResponse response = new UserProfileResponse();
        response.setUserId(user.getId().toString());
        response.setNickname(user.getNickname());
        response.setAvatarUrl(user.getAvatarUrl());
        response.setGender(user.getGender());
        response.setCity(user.getCity());
        response.setProvince(user.getProvince());
        response.setCountry(user.getCountry());
        response.setIsPremium(user.isValidPremium());
        response.setPremiumExpireTime(user.getPremiumExpireTime());
        response.setDailyUsageCount(user.getDailyUsageCount());
        response.setTotalUsageCount(user.getTotalUsageCount());
        response.setCreateTime(user.getCreateTime());
        response.setLastUsageTime(user.getLastUsageTime());
        return response;
    }

    /**
     * 用户信息响应DTO
     */
    public static class UserProfileResponse {
        private String userId;
        private String nickname;
        private String avatarUrl;
        private Integer gender;
        private String city;
        private String province;
        private String country;
        private Boolean isPremium;
        private java.time.LocalDateTime premiumExpireTime;
        private Integer dailyUsageCount;
        private Integer totalUsageCount;
        private java.time.LocalDateTime createTime;
        private java.time.LocalDateTime lastUsageTime;

        // Getters and Setters
        public String getUserId() { return userId; }
        public void setUserId(String userId) { this.userId = userId; }
        public String getNickname() { return nickname; }
        public void setNickname(String nickname) { this.nickname = nickname; }
        public String getAvatarUrl() { return avatarUrl; }
        public void setAvatarUrl(String avatarUrl) { this.avatarUrl = avatarUrl; }
        public Integer getGender() { return gender; }
        public void setGender(Integer gender) { this.gender = gender; }
        public String getCity() { return city; }
        public void setCity(String city) { this.city = city; }
        public String getProvince() { return province; }
        public void setProvince(String province) { this.province = province; }
        public String getCountry() { return country; }
        public void setCountry(String country) { this.country = country; }
        public Boolean getIsPremium() { return isPremium; }
        public void setIsPremium(Boolean isPremium) { this.isPremium = isPremium; }
        public java.time.LocalDateTime getPremiumExpireTime() { return premiumExpireTime; }
        public void setPremiumExpireTime(java.time.LocalDateTime premiumExpireTime) { this.premiumExpireTime = premiumExpireTime; }
        public Integer getDailyUsageCount() { return dailyUsageCount; }
        public void setDailyUsageCount(Integer dailyUsageCount) { this.dailyUsageCount = dailyUsageCount; }
        public Integer getTotalUsageCount() { return totalUsageCount; }
        public void setTotalUsageCount(Integer totalUsageCount) { this.totalUsageCount = totalUsageCount; }
        public java.time.LocalDateTime getCreateTime() { return createTime; }
        public void setCreateTime(java.time.LocalDateTime createTime) { this.createTime = createTime; }
        public java.time.LocalDateTime getLastUsageTime() { return lastUsageTime; }
        public void setLastUsageTime(java.time.LocalDateTime lastUsageTime) { this.lastUsageTime = lastUsageTime; }
    }

    /**
     * 更新用户信息请求DTO
     */
    public static class UpdateProfileRequest {
        private String nickname;
        private String avatarUrl;

        public String getNickname() { return nickname; }
        public void setNickname(String nickname) { this.nickname = nickname; }
        public String getAvatarUrl() { return avatarUrl; }
        public void setAvatarUrl(String avatarUrl) { this.avatarUrl = avatarUrl; }
    }

    /**
     * 配额响应DTO
     */
    public static class QuotaResponse {
        private Integer dailyLimit;
        private Integer usedToday;
        private Integer remainingToday;
        private Boolean isPremium;
        private java.time.LocalDateTime premiumExpireTime;

        public Integer getDailyLimit() { return dailyLimit; }
        public void setDailyLimit(Integer dailyLimit) { this.dailyLimit = dailyLimit; }
        public Integer getUsedToday() { return usedToday; }
        public void setUsedToday(Integer usedToday) { this.usedToday = usedToday; }
        public Integer getRemainingToday() { return remainingToday; }
        public void setRemainingToday(Integer remainingToday) { this.remainingToday = remainingToday; }
        public Boolean getIsPremium() { return isPremium; }
        public void setIsPremium(Boolean isPremium) { this.isPremium = isPremium; }
        public java.time.LocalDateTime getPremiumExpireTime() { return premiumExpireTime; }
        public void setPremiumExpireTime(java.time.LocalDateTime premiumExpireTime) { this.premiumExpireTime = premiumExpireTime; }
    }
}
