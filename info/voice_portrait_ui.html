<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>语音肖像生成小程序UI</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .phone-container {
            width: 375px;
            height: 812px;
            margin: 20px auto;
            background: #000;
            border-radius: 40px;
            padding: 10px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
            position: relative;
        }

        .screen {
            width: 100%;
            height: 100%;
            background: #fff;
            border-radius: 30px;
            overflow: hidden;
            position: relative;
        }

        .status-bar {
            height: 44px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }

        .page {
            display: none;
            height: calc(100% - 44px);
            background: #f8f9fa;
        }

        .page.active {
            display: block;
        }

        /* 首页样式 */
        .home-page {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 40px 20px;
            text-align: center;
        }

        .logo {
            width: 120px;
            height: 120px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }

        .logo svg {
            width: 60px;
            height: 60px;
            fill: white;
        }

        .welcome-text {
            margin-bottom: 40px;
        }

        .welcome-text h1 {
            font-size: 28px;
            color: #2d3748;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .welcome-text p {
            font-size: 16px;
            color: #718096;
            line-height: 1.6;
        }

        .feature-list {
            width: 100%;
            margin-bottom: 40px;
        }

        .feature-item {
            display: flex;
            align-items: center;
            padding: 15px;
            background: white;
            border-radius: 12px;
            margin-bottom: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .feature-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
        }

        .start-btn {
            width: 100%;
            padding: 18px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 25px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .start-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(102, 126, 234, 0.5);
        }

        /* 录音页面样式 */
        .record-page {
            padding: 30px 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .record-title {
            font-size: 24px;
            color: #2d3748;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .record-subtitle {
            font-size: 16px;
            color: #718096;
            margin-bottom: 40px;
            text-align: center;
            line-height: 1.5;
        }

        .voice-visualizer {
            width: 280px;
            height: 280px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 30px;
            position: relative;
            box-shadow: 0 15px 40px rgba(102, 126, 234, 0.3);
        }

        .voice-wave {
            position: absolute;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            border: 3px solid rgba(255,255,255,0.3);
            animation: pulse 2s infinite;
        }

        .voice-wave:nth-child(2) {
            animation-delay: 0.5s;
        }

        .voice-wave:nth-child(3) {
            animation-delay: 1s;
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
                opacity: 1;
            }
            100% {
                transform: scale(1.2);
                opacity: 0;
            }
        }

        .record-btn {
            width: 80px;
            height: 80px;
            background: white;
            border-radius: 50%;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 10;
        }

        .record-btn.recording {
            background: #e53e3e;
        }

        .record-controls {
            display: flex;
            gap: 20px;
            margin-bottom: 30px;
        }

        .control-btn {
            padding: 12px 24px;
            border: 2px solid #667eea;
            background: transparent;
            color: #667eea;
            border-radius: 20px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .control-btn:hover, .control-btn.active {
            background: #667eea;
            color: white;
        }

        .timer {
            font-size: 18px;
            color: #2d3748;
            font-weight: 600;
            margin-bottom: 20px;
        }

        /* 设置页面样式 */
        .settings-page {
            padding: 30px 20px;
        }

        .settings-section {
            margin-bottom: 30px;
        }

        .section-title {
            font-size: 18px;
            color: #2d3748;
            font-weight: 600;
            margin-bottom: 15px;
        }

        .option-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
        }

        .option-card {
            padding: 15px;
            background: white;
            border-radius: 12px;
            border: 2px solid #e2e8f0;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .option-card.selected {
            border-color: #667eea;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .continue-btn {
            width: 100%;
            padding: 18px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 25px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            margin-top: 30px;
        }

        /* 生成页面样式 */
        .generate-page {
            padding: 30px 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
        }

        .loading-animation {
            width: 200px;
            height: 200px;
            border-radius: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 30px;
            position: relative;
            overflow: hidden;
        }

        .loading-spinner {
            width: 60px;
            height: 60px;
            border: 4px solid rgba(255,255,255,0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e2e8f0;
            border-radius: 4px;
            margin-bottom: 20px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 4px;
            width: 0%;
            transition: width 0.3s ease;
        }

        /* 结果页面样式 */
        .result-page {
            padding: 20px;
        }

        .result-portrait {
            width: 100%;
            height: 300px;
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            border-radius: 20px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
        }

        .result-actions {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }

        .action-btn {
            padding: 15px;
            border: 2px solid #667eea;
            background: white;
            color: #667eea;
            border-radius: 12px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .action-btn:hover {
            background: #667eea;
            color: white;
        }

        .share-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
        }

        /* 底部导航 */
        .bottom-nav {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: white;
            border-top: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-around;
            align-items: center;
            padding-bottom: 20px;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            cursor: pointer;
            padding: 8px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .nav-item.active {
            background: rgba(102, 126, 234, 0.1);
        }

        .nav-icon {
            width: 24px;
            height: 24px;
            margin-bottom: 4px;
            fill: #718096;
        }

        .nav-item.active .nav-icon {
            fill: #667eea;
        }

        .nav-text {
            font-size: 12px;
            color: #718096;
        }

        .nav-item.active .nav-text {
            color: #667eea;
        }
    </style>
</head>
<body>
    <div class="phone-container">
        <div class="screen">
            <div class="status-bar">
                <span>9:41</span>
                <span>语音肖像</span>
                <span>🔋100%</span>
            </div>

            <!-- 首页 -->
            <div class="page active" id="home">
                <div class="home-page">
                    <div class="logo">
                        <svg viewBox="0 0 24 24">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zM12 6c1.93 0 3.5 1.57 3.5 3.5S13.93 13 12 13s-3.5-1.57-3.5-3.5S10.07 6 12 6zm0 12c-2.03 0-4.43-.82-6.14-2.88C7.55 14.8 9.68 14 12 14s4.45.8 6.14 2.12C16.43 17.18 14.03 18 12 18z"/>
                        </svg>
                    </div>
                    
                    <div class="welcome-text">
                        <h1>声音肖像师</h1>
                        <p>用你的声音，画出独特的肖像<br>让AI听见你的美</p>
                    </div>
                    
                    <div class="feature-list">
                        <div class="feature-item">
                            <div class="feature-icon">🎤</div>
                            <div>
                                <div style="font-weight: 600; margin-bottom: 4px;">录制语音</div>
                                <div style="color: #718096; font-size: 14px;">录制10-30秒的语音内容</div>
                            </div>
                        </div>
                        <div class="feature-item">
                            <div class="feature-icon">🤖</div>
                            <div>
                                <div style="font-weight: 600; margin-bottom: 4px;">AI分析</div>
                                <div style="color: #718096; font-size: 14px;">智能分析语音特征和情感</div>
                            </div>
                        </div>
                        <div class="feature-item">
                            <div class="feature-icon">🎨</div>
                            <div>
                                <div style="font-weight: 600; margin-bottom: 4px;">生成肖像</div>
                                <div style="color: #718096; font-size: 14px;">创造独一无二的艺术肖像</div>
                            </div>
                        </div>
                    </div>
                    
                    <button class="start-btn" onclick="showPage('record')">
                        开始创作
                    </button>
                </div>
            </div>

            <!-- 录音页面 -->
            <div class="page" id="record">
                <div class="record-page">
                    <h2 class="record-title">录制你的声音</h2>
                    <p class="record-subtitle">请录制10-30秒的语音<br>可以是自我介绍、朗诵或唱歌</p>
                    
                    <div class="voice-visualizer">
                        <div class="voice-wave"></div>
                        <div class="voice-wave"></div>
                        <div class="voice-wave"></div>
                        <button class="record-btn" id="recordBtn" onclick="toggleRecording()">
                            <svg width="32" height="32" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z"/>
                                <path d="M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z"/>
                            </svg>
                        </button>
                    </div>
                    
                    <div class="timer" id="timer">00:00</div>
                    
                    <div class="record-controls">
                        <button class="control-btn" onclick="resetRecording()">重新录制</button>
                        <button class="control-btn" onclick="showPage('settings')" id="nextBtn" style="display: none;">下一步</button>
                    </div>
                </div>
            </div>

            <!-- 设置页面 -->
            <div class="page" id="settings">
                <div class="settings-page">
                    <div class="settings-section">
                        <h3 class="section-title">选择艺术风格</h3>
                        <div class="option-grid">
                            <div class="option-card selected" onclick="selectOption(this)">
                                <div style="font-size: 24px; margin-bottom: 8px;">🎨</div>
                                <div>写实风格</div>
                            </div>
                            <div class="option-card" onclick="selectOption(this)">
                                <div style="font-size: 24px; margin-bottom: 8px;">🖼️</div>
                                <div>卡通风格</div>
                            </div>
                            <div class="option-card" onclick="selectOption(this)">
                                <div style="font-size: 24px; margin-bottom: 8px;">🌸</div>
                                <div>水彩风格</div>
                            </div>
                            <div class="option-card" onclick="selectOption(this)">
                                <div style="font-size: 24px; margin-bottom: 8px;">✏️</div>
                                <div>素描风格</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="settings-section">
                        <h3 class="section-title">性别偏好</h3>
                        <div class="option-grid">
                            <div class="option-card selected" onclick="selectOption(this)">
                                <div style="font-size: 24px; margin-bottom: 8px;">👤</div>
                                <div>自动识别</div>
                            </div>
                            <div class="option-card" onclick="selectOption(this)">
                                <div style="font-size: 24px; margin-bottom: 8px;">👨</div>
                                <div>男性</div>
                            </div>
                            <div class="option-card" onclick="selectOption(this)">
                                <div style="font-size: 24px; margin-bottom: 8px;">👩</div>
                                <div>女性</div>
                            </div>
                            <div class="option-card" onclick="selectOption(this)">
                                <div style="font-size: 24px; margin-bottom: 8px;">🌈</div>
                                <div>中性</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="settings-section">
                        <h3 class="section-title">色彩主题</h3>
                        <div class="option-grid">
                            <div class="option-card selected" onclick="selectOption(this)">
                                <div style="font-size: 24px; margin-bottom: 8px;">🌟</div>
                                <div>暖色调</div>
                            </div>
                            <div class="option-card" onclick="selectOption(this)">
                                <div style="font-size: 24px; margin-bottom: 8px;">❄️</div>
                                <div>冷色调</div>
                            </div>
                            <div class="option-card" onclick="selectOption(this)">
                                <div style="font-size: 24px; margin-bottom: 8px;">🖤</div>
                                <div>黑白</div>
                            </div>
                            <div class="option-card" onclick="selectOption(this)">
                                <div style="font-size: 24px; margin-bottom: 8px;">🌈</div>
                                <div>彩虹</div>
                            </div>
                        </div>
                    </div>
                    
                    <button class="continue-btn" onclick="showPage('generate')">
                        开始生成肖像
                    </button>
                </div>
            </div>

            <!-- 生成页面 -->
            <div class="page" id="generate">
                <div class="generate-page">
                    <div class="loading-animation">
                        <div class="loading-spinner"></div>
                    </div>
                    
                    <h2 style="font-size: 24px; color: #2d3748; margin-bottom: 10px;">AI正在创作中...</h2>
                    <p style="color: #718096; margin-bottom: 30px;">分析语音特征并生成专属肖像</p>
                    
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    
                    <div id="progressText" style="color: #718096; font-size: 14px;">正在分析语音特征... 0%</div>
                </div>
            </div>

            <!-- 结果页面 -->
            <div class="page" id="result">
                <div class="result-page">
                    <div class="result-portrait">
                        <div style="text-align: center;">
                            <div style="font-size: 48px; margin-bottom: 10px;">🎨</div>
                            <div>你的专属肖像</div>
                            <div style="font-size: 14px; opacity: 0.8; margin-top: 5px;">基于语音特征生成</div>
                        </div>
                    </div>
                    
                    <div style="text-align: center; margin-bottom: 20px;">
                        <h3 style="font-size: 20px; color: #2d3748; margin-bottom: 5px;">创作完成！</h3>
                        <p style="color: #718096; font-size: 14px;">这是根据你的声音特征生成的独特肖像</p>
                    </div>
                    
                    <div class="result-actions">
                        <button class="action-btn" onclick="regenerate()">
                            <div style="margin-bottom: 4px;">🔄</div>
                            重新生成
                        </button>
                        <button class="action-btn" onclick="adjustStyle()">
                            <div style="margin-bottom: 4px;">⚙️</div>
                            调整风格
                        </button>
                        <button class="action-btn" onclick="saveImage()">
                            <div style="margin-bottom: 4px;">💾</div>
                            保存图片
                        </button>
                        <button class="action-btn share-btn" onclick="shareImage()">
                            <div style="margin-bottom: 4px;">📤</div>
                            分享作品
                        </button>
                    </div>
                    
                    <button class="continue-btn" onclick="showPage('home')">
                        返回首页
                    </button>
                </div>
            </div>

            <!-- 底部导航 -->
            <div class="bottom-nav">
                <div class="nav-item active" onclick="showPage('home')">
                    <svg class="nav-icon" viewBox="0 0 24 24">
                        <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
                    </svg>
                    <span class="nav-text">首页</span>
                </div>
                <div class="nav-item" onclick="showPage('record')">
                    <svg class="nav-icon" viewBox="0 0 24 24">
                        <path d="M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z"/>
                        <path d="M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z"/>
                    </svg>
                    <span class="nav-text">录音</span>
                </div>
                <div class="nav-item" onclick="showGallery()">
                    <svg class="nav-icon" viewBox="0 0 24 24">
                        <path d="M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z"/>
                    </svg>
                    <span class="nav-text">作品</span>
                </div>
                <div class="nav-item" onclick="showProfile()">
                    <svg class="nav-icon" viewBox="0 0 24 24">
                        <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                    </svg>
                    <span class="nav-text">我的</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        let isRecording = false;
        let recordingTime = 0;
        let recordingInterval;
        let progressInterval;

        function showPage(pageId) {
            // 隐藏所有页面
            document.querySelectorAll('.page').forEach(page => {
                page.classList.remove('active');
            });
            
            // 显示目标页面
            document.getElementById(pageId).classList.add('active');
            
            // 更新导航状态
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            
            // 根据页面更新对应导航
            if (pageId === 'home') {
                document.querySelector('.nav-item:nth-child(1)').classList.add('active');
            } else if (pageId === 'record') {
                document.querySelector('.nav-item:nth-child(2)').classList.add('active');
            }
        }

        function toggleRecording() {
            const recordBtn = document.getElementById('recordBtn');
            const timer = document.getElementById('timer');
            const nextBtn = document.getElementById('nextBtn');
            
            if (!isRecording) {
                // 开始录音
                isRecording = true;
                recordingTime = 0;
                recordBtn.classList.add('recording');
                recordBtn.innerHTML = `
                    <svg width="32" height="32" viewBox="0 0 24 24" fill="currentColor">
                        <rect x="6" y="6" width="12" height="12" rx="2"/>
                    </svg>
                `;
                
                // 启动计时器
                recordingInterval = setInterval(() => {
                    recordingTime++;
                    const minutes = Math.floor(recordingTime / 60);
                    const seconds = recordingTime % 60;
                    timer.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                    
                    // 30秒后自动停止
                    if (recordingTime >= 30) {
                        toggleRecording();
                    }
                }, 1000);
                
            } else {
                // 停止录音
                isRecording = false;
                recordBtn.classList.remove('recording');
                recordBtn.innerHTML = `
                    <svg width="32" height="32" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z"/>
                        <path d="M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z"/>
                    </svg>
                `;
                
                clearInterval(recordingInterval);
                
                // 显示下一步按钮
                if (recordingTime >= 10) {
                    nextBtn.style.display = 'block';
                }
            }
        }

        function resetRecording() {
            if (isRecording) {
                toggleRecording();
            }
            
            recordingTime = 0;
            document.getElementById('timer').textContent = '00:00';
            document.getElementById('nextBtn').style.display = 'none';
        }

        function selectOption(element) {
            // 移除同一组中其他选项的选中状态
            const parent = element.parentElement;
            parent.querySelectorAll('.option-card').forEach(card => {
                card.classList.remove('selected');
            });
            
            // 选中当前选项
            element.classList.add('selected');
        }

        function startGeneration() {
            showPage('generate');
            
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');
            let progress = 0;
            
            const steps = [
                '正在分析语音特征...',
                '正在识别情感色彩...',
                '正在匹配艺术风格...',
                '正在生成肖像画...',
                '正在优化细节...',
                '创作完成！'
            ];
            
            progressInterval = setInterval(() => {
                progress += Math.random() * 15;
                if (progress > 100) progress = 100;
                
                progressFill.style.width = progress + '%';
                
                const stepIndex = Math.floor(progress / 20);
                if (stepIndex < steps.length) {
                    progressText.textContent = steps[stepIndex] + ' ' + Math.floor(progress) + '%';
                }
                
                if (progress >= 100) {
                    clearInterval(progressInterval);
                    setTimeout(() => {
                        showPage('result');
                    }, 1000);
                }
            }, 100);
        }

        function regenerate() {
            showPage('generate');
            startGeneration();
        }

        function adjustStyle() {
            showPage('settings');
        }

        function saveImage() {
            alert('图片已保存到相册！');
        }

        function shareImage() {
            alert('正在打开分享面板...');
        }

        function showGallery() {
            alert('作品集功能开发中...');
        }

        function showProfile() {
            alert('个人中心功能开发中...');
        }

        // 当显示生成页面时自动开始生成
        function showPage(pageId) {
            document.querySelectorAll('.page').forEach(page => {
                page.classList.remove('active');
            });
            
            document.getElementById(pageId).classList.add('active');
            
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            
            if (pageId === 'home') {
                document.querySelector('.nav-item:nth-child(1)').classList.add('active');
            } else if (pageId === 'record') {
                document.querySelector('.nav-item:nth-child(2)').classList.add('active');
            } else if (pageId === 'generate') {
                startGeneration();
            }
        }
    </script>
</body>
</html>