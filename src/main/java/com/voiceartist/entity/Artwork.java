package com.voiceartist.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;

/**
 * 作品实体类
 */
@Data
@Entity
@Table(name = "artworks")
@EqualsAndHashCode(callSuper = false)
public class Artwork {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 作品唯一标识
     */
    @Column(name = "artwork_id", unique = true, nullable = false)
    private String artworkId;

    /**
     * 用户ID
     */
    @Column(name = "user_id", nullable = false)
    private Long userId;

    /**
     * 肖像任务ID
     */
    @Column(name = "portrait_task_id", nullable = false)
    private Long portraitTaskId;

    /**
     * 作品标题
     */
    @Column(name = "title")
    private String title;

    /**
     * 作品描述
     */
    @Column(name = "description")
    private String description;

    /**
     * 图片URL
     */
    @Column(name = "image_url", nullable = false)
    private String imageUrl;

    /**
     * 缩略图URL
     */
    @Column(name = "thumbnail_url")
    private String thumbnailUrl;

    /**
     * 图片宽度
     */
    @Column(name = "image_width")
    private Integer imageWidth;

    /**
     * 图片高度
     */
    @Column(name = "image_height")
    private Integer imageHeight;

    /**
     * 文件大小（字节）
     */
    @Column(name = "file_size")
    private Long fileSize;

    /**
     * 艺术风格
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "art_style", nullable = false)
    private PortraitTask.ArtStyle artStyle;

    /**
     * 性别偏好
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "gender_preference", nullable = false)
    private PortraitTask.GenderPreference genderPreference;

    /**
     * 色彩主题
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "color_theme", nullable = false)
    private PortraitTask.ColorTheme colorTheme;

    /**
     * 生成设置JSON
     */
    @Column(name = "generation_settings", columnDefinition = "TEXT")
    private String generationSettings;

    /**
     * 是否公开展示
     */
    @Column(name = "is_public", nullable = false, columnDefinition = "BOOLEAN DEFAULT FALSE")
    private Boolean isPublic = false;

    /**
     * 点赞数
     */
    @Column(name = "like_count", nullable = false, columnDefinition = "INT DEFAULT 0")
    private Integer likeCount = 0;

    /**
     * 分享次数
     */
    @Column(name = "share_count", nullable = false, columnDefinition = "INT DEFAULT 0")
    private Integer shareCount = 0;

    /**
     * 查看次数
     */
    @Column(name = "view_count", nullable = false, columnDefinition = "INT DEFAULT 0")
    private Integer viewCount = 0;

    /**
     * 作品状态：ACTIVE-正常，DELETED-已删除，HIDDEN-隐藏
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private ArtworkStatus status = ArtworkStatus.ACTIVE;

    /**
     * 创建时间
     */
    @CreationTimestamp
    @Column(name = "create_time", nullable = false, updatable = false)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @UpdateTimestamp
    @Column(name = "update_time", nullable = false)
    private LocalDateTime updateTime;

    /**
     * 作品状态枚举
     */
    public enum ArtworkStatus {
        ACTIVE("正常"),
        DELETED("已删除"),
        HIDDEN("隐藏");

        private final String description;

        ArtworkStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
