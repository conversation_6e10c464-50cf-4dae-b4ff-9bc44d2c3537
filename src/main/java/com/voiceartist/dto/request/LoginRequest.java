package com.voiceartist.dto.request;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 微信小程序登录请求DTO
 */
@Data
public class LoginRequest {

    /**
     * 微信小程序登录凭证
     */
    @NotBlank(message = "登录凭证不能为空")
    private String code;

    /**
     * 用户信息（可选）
     */
    private WechatUserInfo userInfo;

    @Data
    public static class WechatUserInfo {
        /**
         * 用户昵称
         */
        private String nickName;

        /**
         * 用户头像URL
         */
        private String avatarUrl;

        /**
         * 性别：0-未知，1-男，2-女
         */
        private Integer gender;

        /**
         * 城市
         */
        private String city;

        /**
         * 省份
         */
        private String province;

        /**
         * 国家
         */
        private String country;
    }
}
