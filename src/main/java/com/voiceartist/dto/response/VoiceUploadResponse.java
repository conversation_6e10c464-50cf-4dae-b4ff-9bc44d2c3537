package com.voiceartist.dto.response;

import com.voiceartist.entity.VoiceRecord;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 语音上传响应DTO
 */
@Data
public class VoiceUploadResponse {

    /**
     * 语音记录ID
     */
    private String voiceId;

    /**
     * 文件URL
     */
    private String fileUrl;

    /**
     * 音频时长（秒）
     */
    private Integer duration;

    /**
     * 文件大小（字节）
     */
    private Long fileSize;

    /**
     * 音频格式
     */
    private String format;

    /**
     * 处理状态
     */
    private VoiceRecord.ProcessStatus status;

    /**
     * 上传时间
     */
    private LocalDateTime createTime;
}
