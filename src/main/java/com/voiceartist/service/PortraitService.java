package com.voiceartist.service;

import com.voiceartist.dto.request.PortraitGenerateRequest;
import com.voiceartist.dto.response.PortraitTaskResponse;
import com.voiceartist.entity.PortraitTask;
import com.voiceartist.entity.VoiceRecord;
import com.voiceartist.exception.BusinessException;
import com.voiceartist.repository.PortraitTaskRepository;
import com.voiceartist.repository.VoiceRecordRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Optional;
import java.util.UUID;

/**
 * 肖像生成服务类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PortraitService {

    private final PortraitTaskRepository portraitTaskRepository;
    private final VoiceRecordRepository voiceRecordRepository;
    private final UserService userService;

    @Value("${file.upload.image-path}")
    private String imageUploadPath;

    @Value("${ai.service.base-url}")
    private String aiServiceBaseUrl;

    /**
     * 创建肖像生成任务
     */
    @Transactional
    public PortraitTaskResponse generatePortrait(Long userId, PortraitGenerateRequest request) {
        try {
            // 1. 验证用户配额
            if (!userService.checkUsageQuota(userId)) {
                throw new BusinessException("今日使用次数已达上限");
            }

            // 2. 验证语音记录
            VoiceRecord voiceRecord = validateVoiceRecord(request.getVoiceId(), userId);

            // 3. 创建生成任务
            PortraitTask task = createPortraitTask(userId, voiceRecord, request);
            task = portraitTaskRepository.save(task);

            // 4. 增加用户使用次数
            userService.incrementUsageCount(userId);

            // 5. 异步开始生成
            generatePortraitAsync(task.getId());

            // 6. 构建响应
            return buildPortraitTaskResponse(task);

        } catch (Exception e) {
            log.error("创建肖像生成任务失败: userId={}, error={}", userId, e.getMessage(), e);
            if (e instanceof BusinessException) {
                throw e;
            }
            throw new BusinessException("创建生成任务失败，请重试");
        }
    }

    /**
     * 获取任务状态
     */
    public PortraitTaskResponse getTaskStatus(String taskId, Long userId) {
        PortraitTask task = portraitTaskRepository.findByTaskId(taskId)
                .orElseThrow(() -> new BusinessException("任务不存在"));

        // 验证用户权限
        if (!task.getUserId().equals(userId)) {
            throw new BusinessException("无权访问该任务");
        }

        return buildPortraitTaskResponse(task);
    }

    /**
     * 重新生成肖像
     */
    @Transactional
    public PortraitTaskResponse regeneratePortrait(Long userId, String originalTaskId, 
                                                 PortraitGenerateRequest newRequest) {
        try {
            // 1. 验证原任务
            PortraitTask originalTask = portraitTaskRepository.findByTaskId(originalTaskId)
                    .orElseThrow(() -> new BusinessException("原任务不存在"));

            if (!originalTask.getUserId().equals(userId)) {
                throw new BusinessException("无权访问该任务");
            }

            // 2. 验证用户配额
            if (!userService.checkUsageQuota(userId)) {
                throw new BusinessException("今日使用次数已达上限");
            }

            // 3. 获取语音记录
            VoiceRecord voiceRecord = voiceRecordRepository.findById(originalTask.getVoiceRecordId())
                    .orElseThrow(() -> new BusinessException("语音记录不存在"));

            // 4. 创建新任务
            PortraitTask newTask = createPortraitTask(userId, voiceRecord, newRequest);
            newTask = portraitTaskRepository.save(newTask);

            // 5. 增加用户使用次数
            userService.incrementUsageCount(userId);

            // 6. 异步开始生成
            generatePortraitAsync(newTask.getId());

            return buildPortraitTaskResponse(newTask);

        } catch (Exception e) {
            log.error("重新生成肖像失败: userId={}, originalTaskId={}, error={}", 
                     userId, originalTaskId, e.getMessage(), e);
            if (e instanceof BusinessException) {
                throw e;
            }
            throw new BusinessException("重新生成失败，请重试");
        }
    }

    /**
     * 验证语音记录
     */
    private VoiceRecord validateVoiceRecord(String voiceId, Long userId) {
        VoiceRecord voiceRecord = voiceRecordRepository.findByVoiceId(voiceId)
                .orElseThrow(() -> new BusinessException("语音记录不存在"));

        // 验证用户权限
        if (!voiceRecord.getUserId().equals(userId)) {
            throw new BusinessException("无权访问该语音记录");
        }

        // 验证处理状态
        if (voiceRecord.getStatus() != VoiceRecord.ProcessStatus.COMPLETED) {
            throw new BusinessException("语音还未处理完成，请稍后再试");
        }

        return voiceRecord;
    }

    /**
     * 创建肖像生成任务
     */
    private PortraitTask createPortraitTask(Long userId, VoiceRecord voiceRecord, 
                                          PortraitGenerateRequest request) {
        PortraitTask task = new PortraitTask();
        task.setTaskId(UUID.randomUUID().toString());
        task.setUserId(userId);
        task.setVoiceRecordId(voiceRecord.getId());
        task.setArtStyle(request.getArtStyle());
        task.setGenderPreference(request.getGenderPreference());
        task.setColorTheme(request.getColorTheme());
        task.setBackgroundSetting(request.getBackgroundSetting());
        task.setStatus(PortraitTask.TaskStatus.PENDING);
        task.setProgress(0);
        task.setEstimatedTime(180); // 预计3分钟

        // 构建生成参数JSON
        String generationParams = String.format("""
            {
                "artStyle": "%s",
                "genderPreference": "%s",
                "colorTheme": "%s",
                "backgroundSetting": "%s",
                "title": "%s",
                "description": "%s"
            }
            """, 
            request.getArtStyle(),
            request.getGenderPreference(),
            request.getColorTheme(),
            request.getBackgroundSetting() != null ? request.getBackgroundSetting() : "",
            request.getTitle() != null ? request.getTitle() : "",
            request.getDescription() != null ? request.getDescription() : ""
        );
        task.setGenerationParams(generationParams);

        return task;
    }

    /**
     * 异步生成肖像
     */
    @Async
    public void generatePortraitAsync(Long taskId) {
        try {
            log.info("开始生成肖像: taskId={}", taskId);

            Optional<PortraitTask> optionalTask = portraitTaskRepository.findById(taskId);
            if (optionalTask.isEmpty()) {
                log.error("任务不存在: taskId={}", taskId);
                return;
            }

            PortraitTask task = optionalTask.get();
            
            // 更新任务状态为处理中
            task.setStatus(PortraitTask.TaskStatus.PROCESSING);
            task.setStartTime(LocalDateTime.now());
            task.setProgress(10);
            portraitTaskRepository.save(task);

            // 获取语音记录和特征
            VoiceRecord voiceRecord = voiceRecordRepository.findById(task.getVoiceRecordId())
                    .orElseThrow(() -> new BusinessException("语音记录不存在"));

            // 模拟AI生成过程
            simulatePortraitGeneration(task);

            // TODO: 实际调用AI服务
            // String imageUrl = callAIService(voiceRecord, task);

            // 模拟生成的图片URL
            String imageUrl = "/api/v1/images/" + task.getTaskId() + ".jpg";

            // 更新任务完成状态
            task.setStatus(PortraitTask.TaskStatus.COMPLETED);
            task.setProgress(100);
            task.setResultImageUrl(imageUrl);
            task.setCompleteTime(LocalDateTime.now());
            portraitTaskRepository.save(task);

            log.info("肖像生成完成: taskId={}, imageUrl={}", taskId, imageUrl);

        } catch (Exception e) {
            log.error("肖像生成失败: taskId={}, error={}", taskId, e.getMessage(), e);

            // 更新失败状态
            portraitTaskRepository.findById(taskId).ifPresent(task -> {
                task.setStatus(PortraitTask.TaskStatus.FAILED);
                task.setErrorMessage(e.getMessage());
                task.setCompleteTime(LocalDateTime.now());
                portraitTaskRepository.save(task);
            });
        }
    }

    /**
     * 模拟肖像生成过程
     */
    private void simulatePortraitGeneration(PortraitTask task) throws InterruptedException {
        String[] steps = {
            "分析语音特征...",
            "识别情感色彩...",
            "匹配艺术风格...",
            "生成肖像画...",
            "优化细节...",
            "完成创作..."
        };

        for (int i = 0; i < steps.length; i++) {
            Thread.sleep(2000); // 模拟处理时间
            
            int progress = 10 + (i + 1) * 15;
            task.setProgress(Math.min(progress, 95));
            portraitTaskRepository.save(task);
            
            log.info("生成进度: taskId={}, step={}, progress={}%", 
                    task.getId(), steps[i], task.getProgress());
        }
    }

    /**
     * 构建任务响应
     */
    private PortraitTaskResponse buildPortraitTaskResponse(PortraitTask task) {
        PortraitTaskResponse response = new PortraitTaskResponse();
        response.setTaskId(task.getTaskId());
        response.setStatus(task.getStatus());
        response.setProgress(task.getProgress());
        response.setEstimatedTime(task.getEstimatedTime());
        response.setErrorMessage(task.getErrorMessage());
        response.setCreateTime(task.getCreateTime());
        response.setCompleteTime(task.getCompleteTime());

        // 如果任务完成，构建结果信息
        if (task.getStatus() == PortraitTask.TaskStatus.COMPLETED && task.getResultImageUrl() != null) {
            PortraitTaskResponse.GenerationResult result = new PortraitTaskResponse.GenerationResult();
            result.setImageUrl(task.getResultImageUrl());
            result.setThumbnailUrl(task.getResultImageUrl() + "?thumbnail=true");
            result.setImageWidth(512);  // 默认尺寸
            result.setImageHeight(512);
            result.setFileSize(1024L * 1024L); // 默认1MB
            result.setArtStyle(task.getArtStyle());
            result.setGenderPreference(task.getGenderPreference());
            result.setColorTheme(task.getColorTheme());
            response.setResult(result);
        }

        return response;
    }
}
